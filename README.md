# 智能主播助手 - Vue.js 前端

专业的直播助手工具，具备弹幕监控功能，使用 Vue 3 + Element Plus + WebSocket 构建。

# Intelligent Anchor Assistant - Vue.js Frontend

A professional live streaming assistant tool with bullet comment (danmaku) monitoring capabilities, built with Vue 3 + Element Plus + WebSocket. **Fully localized in Chinese and works completely offline with advanced mock data simulation.**

## Features

### 🎯 核心功能 / Core Functionality
- **实时弹幕监控 / Real-time Bullet Comment Monitoring**: 实时评论流与处理状态显示 / Live feed of incoming comments with processing status
- **自动回复系统 / Automated Response System**: AI驱动的意图分析和自动回复 / AI-powered intent analysis and automated responses
- **进程管理 / Process Management**: 监控直播进程并控制自动化目标 / Monitor streaming processes and control automation targets
- **WebSocket集成 / WebSocket Integration**: 与Python后端实时通信 / Real-time communication with Python backend
- **会话导出 / Session Export**: 导出评论数据到Excel文件 / Export comment data to Excel files

### 🎨 用户界面 / User Interface
- **专业直播工具美学 / Professional Live Streaming Aesthetic**: 为直播工具优化的现代深色主题界面 / Modern, dark-themed interface optimized for streaming tools
- **双面板仪表板 / Two-Panel Dashboard**: 评论流和详细分析的分屏视图 / Split view for comment feed and detailed analysis
- **响应式设计 / Responsive Design**: 为pywebview嵌入和各种屏幕尺寸优化 / Optimized for pywebview embedding and various screen sizes
- **实时状态指示器 / Real-time Status Indicators**: 连接和处理状态的视觉反馈 / Visual feedback for connection and processing states
- **完整中文本地化 / Complete Chinese Localization**: 所有界面文本均为中文 / All interface text in Chinese
- **完全离线运行 / Fully Offline Operation**: 无需后端服务即可运行所有功能 / All features work without backend services
- **高级模拟数据 / Advanced Mock Data**: 真实的弹幕评论模拟系统 / Realistic bullet comment simulation system

### ⚙️ 配置 / Configuration
- **路径设置 / Path Settings**: 配置截图和模型存储位置 / Configure screenshot and model storage locations
- **快捷键管理 / Hotkey Management**: 设置自定义键盘快捷键 / Set custom keyboard shortcuts for quick actions
- **进程配置 / Process Configuration**: 定义直播和目标进程名称 / Define streaming and target process names

## Technology Stack

- **Vue 3**: Progressive JavaScript framework with Composition API
- **Element Plus**: Professional UI component library
- **Pinia**: State management for Vue 3
- **Vite**: Fast build tool and development server
- **Axios**: HTTP client for API communication
- **WebSocket**: Real-time bidirectional communication

## Project Structure

```
src/
├── api/                 # Centralized API service layer
│   └── index.js        # API endpoints and mock data
├── components/         # Reusable Vue components
│   ├── CommentFeed.vue # Real-time comment feed
│   ├── CommentDetails.vue # Detailed comment view
│   └── ConfigModal.vue # Settings configuration
├── stores/             # Pinia state management
│   ├── auth.js        # Authentication state
│   ├── process.js     # Process management state
│   └── websocket.js   # WebSocket connection state
├── styles/            # Global styles and themes
│   └── global.css     # CSS variables and utilities
├── views/             # Page components
│   ├── LoginView.vue  # Authentication screen
│   └── DashboardView.vue # Main application interface
├── router/            # Vue Router configuration
│   └── index.js       # Route definitions
├── App.vue            # Root component
└── main.js            # Application entry point
```

## Development Setup

### Prerequisites
- Node.js 16+ and npm/yarn
- Python backend service (for production)

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Access the application**:
   - Open http://localhost:3000 in your browser
   - Use any room name to login (mock mode enabled)

### Development Features

- **Full Mock Mode**: Complete offline functionality without backend dependencies
- **Hot Reload**: Instant updates during development
- **Realistic Comment Simulation**: Advanced Chinese bullet comment generation with varied content, statuses, and processing flows
- **Offline Authentication**: Mock login system that works without network connectivity
- **Simulated Process Control**: Mock monitoring and automation that responds realistically

## Configuration

### Environment Variables

Create `.env.local` for custom configuration:

```env
# All functionality works in mock mode by default
VITE_MOCK_MODE=true
VITE_DEBUG=true
# Backend URLs not required for mock mode
VITE_API_BASE_URL=mock://api
VITE_WS_BASE_URL=mock://websocket
```

### Backend Integration

The frontend is designed to work with a Python backend. Key integration points:

- **Authentication**: POST `/api/auth/login` with room name
- **Process Control**: Start/stop monitoring and automation
- **WebSocket**: Real-time comment streaming at `/ws`
- **Configuration**: Save/load settings via REST API
- **Export**: Trigger Excel export generation

## Building for Production

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## PyWebView Integration

The application is optimized for embedding in pywebview:

- **No External Dependencies**: All assets bundled locally
- **Responsive Layout**: Adapts to webview container size
- **Error Handling**: Graceful fallbacks for network issues
- **Auto-reconnection**: WebSocket reconnection logic

## Usage

### 1. Authentication
- Enter your live room name on the login screen
- Click "Connect to Room" to authenticate

### 2. Process Configuration
- Set "Live Streaming Process Name" (e.g., "OBS Studio")
- Set "Automation Target Process Name" (e.g., "Game.exe")
- Click start buttons to begin monitoring/automation

### 3. Comment Monitoring
- View real-time comments in the left panel
- Click any comment to see detailed analysis in the right panel
- Monitor processing status and intent analysis
- Retry failed comment processing if needed

### 4. Configuration
- Click "Settings" to open configuration modal
- Set storage paths for screenshots and models
- Configure hotkeys for quick toggle actions
- Save configuration to backend

### 5. Data Export
- Click "Export Session Data" to generate Excel report
- File is saved on the backend (no frontend download)

## API Integration

All backend communication is centralized in `src/api/index.js`:

```javascript
import { authAPI, processAPI, commentAPI, configAPI } from '@/api'

// Authentication
await authAPI.login(roomName)

// Process control
await processAPI.startMonitoring(processName)

// Comment operations
await commentAPI.retryProcessing(commentId)

// Configuration
await configAPI.updateConfig(settings)
```

## Contributing

1. Follow the existing code style and structure
2. Use TypeScript-style JSDoc comments for functions
3. Maintain responsive design principles
4. Test with mock data before backend integration
5. Update documentation for new features

## License

This project is part of the Intelligent Anchor Assistant system.
