<template>
  <div class="connection-status">
    <!-- 连接状态指示器和文字 -->
    <div class="status-main">
      <!-- 详细状态弹窗 -->
      <el-popover
        placement="bottom"
        :width="320"
        trigger="hover"
        :show-after="500"
        popper-class="connection-status-popover"
      >
        <template #reference>
          <div class="status-indicator">
            <el-icon :class="overallStatus.iconClass" class="status-icon">
              <component :is="overallStatus.icon" />
            </el-icon>
            <span class="status-text">{{ overallStatus.text }}</span>
          </div>
        </template>

      <div class="connection-details">
        <h4>连接状态详情</h4>
        
        <!-- 前端连接状态 -->
        <div class="connection-item">
          <div class="connection-label">
            <el-icon><Monitor /></el-icon>
            前端 ↔ Client端
          </div>
          <el-tag 
            :type="frontendStatus.type" 
            size="small"
            effect="light"
          >
            {{ frontendStatus.text }}
          </el-tag>
        </div>

        <!-- 后端连接状态 -->
        <div class="connection-item">
          <div class="connection-label">
            <el-icon><Connection /></el-icon>
            Client端 ↔ Server端
          </div>
          <el-tag 
            :type="backendStatus.type" 
            size="small"
            effect="light"
          >
            {{ backendStatus.text }}
          </el-tag>
        </div>

        <!-- 连接健康状态 -->
        <div class="connection-item" v-if="websocketStore.isConnectionHealthy">
          <div class="connection-label">
            <el-icon><Timer /></el-icon>
            延迟
          </div>
          <el-tag 
            :type="latencyStatus.type" 
            size="small"
            effect="light"
          >
            {{ latencyStatus.text }}
          </el-tag>
        </div>

        <!-- 重连状态 -->
        <div class="connection-item" v-if="websocketStore.isReconnecting">
          <div class="connection-label">
            <el-icon><Refresh /></el-icon>
            重连状态
          </div>
          <div class="reconnect-info">
            <el-progress 
              :percentage="reconnectProgress" 
              :status="reconnectProgressStatus"
              :stroke-width="6"
            />
            <div class="reconnect-text">
              {{ reconnectAttempts }}/{{ maxReconnectAttempts }} 次尝试
            </div>
          </div>
        </div>

        <!-- 连接状态详情，不显示手动重连按钮 -->
      </div>
    </el-popover>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useWebSocketStore } from '@/stores/websocket'
import { 
  Monitor, 
  Connection, 
  Timer, 
  Refresh,
  CircleCheck,
  Warning,
  CircleClose
} from '@element-plus/icons-vue'

const websocketStore = useWebSocketStore()

// 前端连接状态
const frontendStatus = computed(() => {
  const status = websocketStore.frontendConnectionStatus
  switch (status) {
    case 'connected':
      return { text: '已连接', type: 'success' }
    case 'connecting':
      return { text: '连接中', type: 'warning' }
    case 'error':
      return { text: '连接错误', type: 'danger' }
    default:
      return { text: '未连接', type: 'info' }
  }
})

// 后端连接状态
const backendStatus = computed(() => {
  const status = websocketStore.backendConnectionStatus
  switch (status) {
    case 'connected':
      return { text: '已连接', type: 'success' }
    case 'connecting':
      return { text: '连接中', type: 'warning' }
    case 'error':
      return { text: '连接错误', type: 'danger' }
    default:
      return { text: '未连接', type: 'info' }
  }
})

// 整体连接状态
const overallStatus = computed(() => {
  const status = websocketStore.overallConnectionStatus
  switch (status) {
    case 'fully_connected':
      return { 
        text: '连接正常', 
        type: 'success', 
        icon: CircleCheck,
        iconClass: 'status-icon success'
      }
    case 'partially_connected':
      return { 
        text: '部分连接', 
        type: 'warning', 
        icon: Warning,
        iconClass: 'status-icon warning'
      }
    default:
      return { 
        text: '连接断开', 
        type: 'danger', 
        icon: CircleClose,
        iconClass: 'status-icon danger'
      }
  }
})

// 延迟状态
const latencyStatus = computed(() => {
  const latency = websocketStore.connectionLatency
  if (!latency) return { text: '未知', type: 'info' }
  
  if (latency < 100) {
    return { text: `${latency}ms (优秀)`, type: 'success' }
  } else if (latency < 300) {
    return { text: `${latency}ms (良好)`, type: 'warning' }
  } else {
    return { text: `${latency}ms (较慢)`, type: 'danger' }
  }
})

// 重连进度
const reconnectProgress = computed(() => {
  const progress = websocketStore.reconnectProgress
  if (!progress) return 0
  return Math.round((progress.attempts / progress.maxAttempts) * 100)
})

const reconnectProgressStatus = computed(() => {
  const progress = reconnectProgress.value
  if (progress < 50) return 'success'
  if (progress < 80) return 'warning'
  return 'exception'
})

const reconnectAttempts = computed(() => {
  return websocketStore.reconnectProgress?.attempts || 0
})

const maxReconnectAttempts = computed(() => {
  return websocketStore.reconnectProgress?.maxAttempts || 5
})

// 手动重连功能已移除，连接状态仅用于显示
</script>

<style scoped>
.connection-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 120px; /* 减少最小宽度 */
  max-width: 200px; /* 限制最大宽度 */
}

.status-main {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  white-space: nowrap; /* 防止整体换行 */
}

.status-indicator {
  position: relative;
  flex-shrink: 0; /* 防止图标被压缩 */
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.status-indicator:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.status-icon {
  font-size: 16px;
  transition: all 0.3s ease;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.warning {
  color: #e6a23c;
  animation: pulse 2s infinite;
}

.status-icon.danger {
  color: #f56c6c;
  animation: shake 1s infinite;
}

.status-text {
  font-size: 12px;
  margin-left: 6px; /* 图标和文字之间的间距 */
  white-space: nowrap; /* 防止文字换行 */
  text-align: left; /* 左对齐更自然 */
  flex-shrink: 0; /* 防止文字被压缩 */
  line-height: 1.2; /* 确保行高合适 */
  color: inherit; /* 继承父元素颜色 */
}

.connection-details {
  padding: 8px 0;
}

.connection-details h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.connection-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
}

.reconnect-info {
  flex: 1;
  margin-left: 12px;
}

.reconnect-text {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  text-align: center;
}

.connection-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 全局样式，确保弹出框在最上层 */
:global(.connection-status-popover) {
  z-index: 3000 !important; /* 确保高于其他元素 */
}
</style>
