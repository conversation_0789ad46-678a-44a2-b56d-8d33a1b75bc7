<template>
  <el-dialog
    v-model="visible"
    title="音色选择"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="!isRequired"
    @close="handleClose"
  >
    <div class="voice-selection-content">
      <!-- 游戏选择 -->
      <div class="selection-section">
        <h3 class="section-title">
          <el-icon><VideoPlay /></el-icon>
          选择游戏
        </h3>
        <div class="game-grid">
          <div
            v-for="game in gameList"
            :key="game.gameId"
            class="game-card"
            :class="{ active: selectedGameId === game.gameId }"
            @click="selectGame(game.gameId)"
          >
            <div class="game-icon">
              <el-icon size="24"><Trophy /></el-icon>
            </div>
            <div class="game-name">{{ game.gameName }}</div>
          </div>
        </div>
      </div>

      <!-- 音色选择 -->
      <div class="selection-section" v-if="selectedGameId">
        <h3 class="section-title">
          <el-icon><Microphone /></el-icon>
          选择音色
        </h3>
        <div class="voice-grid">
          <div
            v-for="voice in filteredVoiceList"
            :key="voice.voiceId"
            class="voice-card"
            :class="{ active: selectedVoiceId === voice.voiceId }"
            @click="selectVoice(voice.voiceId)"
          >
            <div class="voice-icon">
              <el-icon size="20">
                <Female v-if="voice.genderId === 'female'" />
                <Male v-else />
              </el-icon>
            </div>
            <div class="voice-info">
              <div class="voice-name">{{ voice.voiceName }}</div>
              <div class="voice-gender">{{ voice.genderId === 'female' ? '女声' : '男声' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择提示 -->
      <div class="selection-hint" v-if="!selectedGameId">
        <el-icon><InfoFilled /></el-icon>
        <span>请先选择游戏，然后选择对应的音色</span>
      </div>

      <div class="selection-hint" v-else-if="!selectedVoiceId">
        <el-icon><InfoFilled /></el-icon>
        <span>请选择一个音色</span>
      </div>

      <!-- 当前选择信息 -->
      <div class="current-selection" v-if="selectedGameId && selectedVoiceId">
        <div class="selection-info">
          <div class="info-item">
            <span class="label">游戏：</span>
            <span class="value">{{ selectedGameName }}</span>
          </div>
          <div class="info-item">
            <span class="label">音色：</span>
            <span class="value">{{ selectedVoiceName }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="!isRequired"
          @click="handleClose"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!selectedGameId || !selectedVoiceId"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  VideoPlay,
  Trophy,
  Microphone,
  Female,
  Male,
  InfoFilled
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isRequired: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

const authStore = useAuthStore()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 数据状态
const loading = ref(false)
const selectedGameId = ref('')
const selectedVoiceId = ref('')

// 计算属性
const gameList = computed(() => authStore.availableGames)
const voiceList = computed(() => authStore.availableVoices)

const filteredVoiceList = computed(() => {
  if (!selectedGameId.value) return []
  return voiceList.value.filter(voice => voice.gameId === selectedGameId.value)
})

const selectedGameName = computed(() => {
  const game = gameList.value.find(g => g.gameId === selectedGameId.value)
  return game?.gameName || ''
})

const selectedVoiceName = computed(() => {
  const voice = voiceList.value.find(v => v.voiceId === selectedVoiceId.value)
  return voice?.voiceName || ''
})

const selectedGenderId = computed(() => {
  const voice = voiceList.value.find(v => v.voiceId === selectedVoiceId.value)
  return voice?.genderId || ''
})

// 方法
const selectGame = (gameId) => {
  if (selectedGameId.value === gameId) return
  
  selectedGameId.value = gameId
  selectedVoiceId.value = '' // 重置音色选择
  console.log('🎮 选择游戏:', gameId)
}

const selectVoice = (voiceId) => {
  selectedVoiceId.value = voiceId
  console.log('🎵 选择音色:', voiceId)
}

const handleConfirm = async () => {
  if (!selectedGameId.value || !selectedVoiceId.value) {
    ElMessage.warning('请选择游戏和音色')
    return
  }

  loading.value = true
  try {
    console.log('🎵 确认音色选择:', {
      gameId: selectedGameId.value,
      voiceId: selectedVoiceId.value,
      genderId: selectedGenderId.value
    })

    const result = await authStore.selectVoiceModel(selectedVoiceId.value, selectedGenderId.value)
    
    if (result.success) {
      ElMessage.success('音色选择成功！')
      emit('confirm', {
        gameId: selectedGameId.value,
        voiceId: selectedVoiceId.value,
        genderId: selectedGenderId.value,
        gameName: selectedGameName.value,
        voiceName: selectedVoiceName.value
      })
      visible.value = false
    } else {
      ElMessage.error(result.error || '音色选择失败')
    }
  } catch (error) {
    console.error('❌ 音色选择异常:', error)
    ElMessage.error('音色选择异常，请重试')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  if (props.isRequired) {
    ElMessage.warning('请先选择音色才能继续使用')
    return
  }
  
  emit('close')
  visible.value = false
}

// 重置选择状态
const resetSelection = () => {
  selectedGameId.value = ''
  selectedVoiceId.value = ''
}

// 监听弹窗显示状态
watch(visible, (newValue) => {
  if (newValue) {
    resetSelection()
  }
})

// 暴露方法给父组件
defineExpose({
  resetSelection
})
</script>

<style scoped>
.voice-selection-content {
  padding: 20px 0;
}

.selection-section {
  margin-bottom: 30px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
}

.game-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(88, 101, 242, 0.5);
}

.game-card.active {
  background: rgba(88, 101, 242, 0.2);
  border-color: #5865f2;
}

.game-icon {
  margin-bottom: 8px;
  color: #5865f2;
}

.game-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
}

.voice-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.voice-card {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.voice-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(88, 101, 242, 0.5);
}

.voice-card.active {
  background: rgba(88, 101, 242, 0.2);
  border-color: #5865f2;
}

.voice-icon {
  margin-right: 12px;
  color: #5865f2;
}

.voice-info {
  flex: 1;
}

.voice-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
}

.voice-gender {
  font-size: 12px;
  color: #a0a0a0;
}

.selection-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  color: #ffc107;
  font-size: 14px;
}

.current-selection {
  padding: 16px;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 6px;
  margin-top: 20px;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #a0a0a0;
  margin-right: 8px;
  min-width: 50px;
}

.value {
  color: #ffffff;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
