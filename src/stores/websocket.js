import { defineStore } from 'pinia'
import { getWebSocketURL } from '@/api'
import { useAuthStore } from '@/stores/auth'

// Check if running in pywebview environment
const isInPyWebView = () => {
  return typeof window !== 'undefined' && window.pywebview && window.pywebview.api
}

export const useWebSocketStore = defineStore('websocket', {
  state: () => ({
    socket: null,
    isConnected: false,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,
    comments: [],
    connectionError: null,
    mockMode: true, // Will be updated in connect() method
    mockConnectionTimer: null,
    commentGenerationTimer: null,
    isMonitoring: false,
    // 新增状态
    autoScroll: true, // 是否自动滚动到最新
    selectedCommentId: null, // 当前选中的弹幕ID
    showScrollToBottom: false, // 是否显示滚动到底部按钮
    monitoringProcess: null, // 当前监控的进程名
    lastCommentTime: null, // 最后一条弹幕的时间

    // 连接状态监控
    connectionStates: {
      frontend: 'disconnected', // 前端与Client端连接状态
      backend: 'disconnected'   // Client端与Server端连接状态
    },
    connectionHealth: {
      lastPing: null,
      latency: null,
      isHealthy: false
    },

    // 重连状态
    isReconnecting: false,
    reconnectTimer: null,
    backoffMultiplier: 1.5,
    maxReconnectInterval: 30000, // 最大重连间隔30秒

    // 会话信息
    currentSessionId: null,
    connectionStartTime: null
  }),

  getters: {
    // DanmuMessage字段格式化工具
    formatDanmuField: () => (field, value) => {
      // 检查空值情况
      if (value === null || value === undefined || value === '' || value === 'None') {
        const emptyDisplayMap = {
          intent: '分析中',
          reply: '生成中',
          method_type: '处理中',
          status: '等待中',
          user: '匿名用户',
          user_game: '未知玩家',
          voice_file_name: '未生成',
          voice_file_status: '未开始'
        }
        return emptyDisplayMap[field] || '未知'
      }

      // 状态字段特殊处理
      if (field === 'status') {
        const statusMap = {
          skipped: '已跳过',
          analyzing: '分析中',
          dealing: '处理中',
          completed: '已完成',
          pending: '等待中',
          processing: '处理中',
          failed: '失败'
        }
        return statusMap[value] || value
      }

      // 处理方式字段特殊处理
      if (field === 'method_type') {
        const methodMap = {
          voice_reply: '语音回复',
          deal: '文字处理',
          analyzing: '分析中'
        }
        return methodMap[value] || value
      }

      // 语音文件状态字段特殊处理
      if (field === 'voice_file_status') {
        const voiceStatusMap = {
          pending: '等待生成',
          generating: '生成中',
          completed: '已完成',
          failed: '生成失败'
        }
        return voiceStatusMap[value] || value
      }

      // 语音文件名字段特殊处理
      if (field === 'voice_file_name') {
        if (value && value.length > 30) {
          return value.substring(0, 30) + '...'
        }
        return value
      }

      return value
    },
    latestComments: (state) => state.comments.slice(-50), // Keep last 50 comments
    connectionStatus: (state) => {
      // 增强连接状态检测
      const hasSocket = !!state.socket
      const socketReady = hasSocket && state.socket.readyState === WebSocket.OPEN
      const frontendConnected = state.connectionStates.frontend === 'connected'

      return (state.isConnected && socketReady && frontendConnected) ? 'connected' : 'disconnected'
    },

    // 双连接状态
    frontendConnectionStatus: (state) => state.connectionStates.frontend,
    backendConnectionStatus: (state) => state.connectionStates.backend,

    // 整体连接状态
    overallConnectionStatus: (state) => {
      if (state.connectionStates.frontend === 'connected' && state.connectionStates.backend === 'connected') {
        return 'fully_connected'
      } else if (state.connectionStates.frontend === 'connected') {
        return 'partially_connected'
      } else {
        return 'disconnected'
      }
    },

    // 连接健康状态
    isConnectionHealthy: (state) => state.connectionHealth.isHealthy,

    // 当前选中的弹幕
    selectedComment: (state) => {
      if (!state.selectedCommentId) {
        return null
      }
      return state.comments.find(comment => comment.id === state.selectedCommentId) || null
    },
    connectionLatency: (state) => state.connectionHealth.latency,

    // 重连状态
    isReconnecting: (state) => state.isReconnecting,
    reconnectProgress: (state) => {
      if (!state.isReconnecting) return null
      return {
        attempts: state.reconnectAttempts,
        maxAttempts: state.maxReconnectAttempts,
        nextRetryIn: state.reconnectTimer ? Math.ceil((state.reconnectTimer - Date.now()) / 1000) : 0
      }
    }
  },

  actions: {
    // TODO: [WebSocket-Vue→Client] 连接到WebSocket服务器
    // 连接URL: ws://localhost:10000
    // 用途: 建立Vue前端与Client端的WebSocket连接
    // 参数: sessionId - 用户会话ID，用于身份认证
    // 响应: 接收welcome消息确认连接成功
    connect(sessionId = null) {
      if (this.isConnected) {
        console.log('WebSocket already connected, skipping...')
        return
      }

      // 如果没有传入sessionId，尝试从localStorage获取
      if (!sessionId) {
        sessionId = localStorage.getItem('session_id')
        console.log('📦 从localStorage获取session_id:', sessionId)
      }

      // 保存会话ID
      this.currentSessionId = sessionId
      this.connectionStartTime = Date.now()
      this.connectionError = null

      // Check environment at runtime
      const isInPyWebViewNow = isInPyWebView()
      this.mockMode = !isInPyWebViewNow

      console.log('WebSocket Connect - Environment Check:')
      console.log('- isInPyWebView:', isInPyWebViewNow)
      console.log('- mockMode:', this.mockMode)
      console.log('- sessionId:', sessionId)
      console.log('- window.pywebview:', typeof window !== 'undefined' ? window.pywebview : 'undefined')

      if (this.mockMode) {
        // Mock WebSocket connection for development
        console.log('Mock WebSocket connecting...')
        this.connectionError = null

        // Simulate connection delay
        this.mockConnectionTimer = setTimeout(() => {
          this.isConnected = true
          this.reconnectAttempts = 0
          console.log('Mock WebSocket connected')
        }, 500)

        return
      }

      // Real WebSocket connection for pywebview
      try {
        const wsUrl = getWebSocketURL(sessionId)
        console.log(`Connecting to WebSocket: ${wsUrl}`)
        this.socket = new WebSocket(wsUrl)

        this.socket.onopen = () => {
          console.log('WebSocket connected to Client端')
          this.isConnected = true
          this.reconnectAttempts = 0
          this.connectionError = null
          try {
            this.isReconnecting = false
          } catch (error) {
            console.warn('设置isReconnecting时出错:', error)
          }

          // 更新连接状态
          this.connectionStates.frontend = 'connected'
          this.connectionStates.backend = 'connected'  // 假设Client端连接正常
          this.connectionHealth.isHealthy = true
          this.connectionHealth.lastPing = Date.now()

          // 清除重连定时器
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer)
            this.reconnectTimer = null
          }

          console.log('✅ WebSocket连接状态已更新:', {
            frontend: this.connectionStates.frontend,
            backend: this.connectionStates.backend,
            isHealthy: this.connectionHealth.isHealthy
          })

          // 如果有session_id，发送给Client端
          if (sessionId) {
            console.log('Sending session_id to client:', sessionId)
            this.socket.send(JSON.stringify({
              type: 'auth',
              session_id: sessionId,
              timestamp: Date.now()
            }))
          }

          // 启动心跳检测
          this.startHeartbeat()
        }

        this.socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)

            // 处理特殊消息类型
            if (data.type === 'pong') {
              this.handlePong(data)
            } else if (data.type === 'connection_status') {
              this.handleConnectionStatus(data)
            } else if (data.type === 'auth_confirmed') {
              console.log('Auth confirmed by Client端:', data)
            } else {
              this.handleMessage(data)
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
          }
        }

        this.socket.onclose = (event) => {
          console.log('WebSocket disconnected from Client端', event.code, event.reason)
          this.isConnected = false
          this.connectionStates.frontend = 'disconnected'
          this.connectionStates.backend = 'disconnected'
          this.socket = null
          this.stopHeartbeat()

          // 更新连接健康状态
          this.connectionHealth.isHealthy = false
          this.connectionHealth.lastPing = null
          this.connectionHealth.latency = null

          // 只有在非主动关闭的情况下才尝试重连
          if (event.code !== 1000) {
            this.attemptReconnect()
          }
        }

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.connectionError = '前端与Client端连接发生错误'
          this.connectionStates.frontend = 'error'
        }
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
        this.connectionError = '连接服务器失败'
      }
    },

    disconnect() {
      console.log('Disconnecting WebSocket...')

      // 停止重连 - 使用try-catch防止Proxy错误
      try {
        this.isReconnecting = false
      } catch (error) {
        console.warn('设置isReconnecting时出错:', error)
      }

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }

      // 停止心跳
      this.stopHeartbeat()

      if (this.mockMode) {
        // Mock WebSocket disconnect
        console.log('Mock WebSocket disconnecting...')
        this.isConnected = false
        this.isMonitoring = false
        this.stopCommentGeneration()

        if (this.mockConnectionTimer) {
          clearTimeout(this.mockConnectionTimer)
          this.mockConnectionTimer = null
        }

        // 重置连接状态
        this.connectionStates.frontend = 'disconnected'
        this.connectionStates.backend = 'disconnected'
        return
      }

      if (this.socket) {
        // 发送断开连接消息
        try {
          this.socket.send(JSON.stringify({
            type: 'disconnect',
            session_id: this.currentSessionId,
            timestamp: Date.now()
          }))
        } catch (error) {
          console.warn('Failed to send disconnect message:', error)
        }

        this.socket.close(1000, 'User initiated disconnect')
        this.socket = null
      }

      // 重置状态
      this.isConnected = false
      this.reconnectAttempts = 0
      this.connectionStates.frontend = 'disconnected'
      this.connectionStates.backend = 'disconnected'
      this.connectionHealth.isHealthy = false
      this.currentSessionId = null
      this.connectionStartTime = null
    },

    attemptReconnect() {
      if (this.isReconnecting) {
        console.log('Reconnection already in progress, skipping...')
        return
      }

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnection attempts reached')
        this.connectionError = '无法重新连接到Client端服务器'
        try {
          this.isReconnecting = false
        } catch (error) {
          console.warn('设置isReconnecting时出错:', error)
        }
        return
      }

      try {
        this.isReconnecting = true
      } catch (error) {
        console.warn('设置isReconnecting时出错:', error)
      }
      this.reconnectAttempts++

      // 保存session_id，因为它可能在重连过程中被重置
      const sessionIdForReconnect = this.currentSessionId

      // 计算重连间隔（指数退避）
      const baseInterval = this.reconnectInterval
      const backoffInterval = Math.min(
        baseInterval * Math.pow(this.backoffMultiplier, this.reconnectAttempts - 1),
        this.maxReconnectInterval
      )

      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${backoffInterval}ms`)

      this.reconnectTimer = setTimeout(() => {
        console.log(`Reconnecting attempt ${this.reconnectAttempts}...`)
        this.connect(sessionIdForReconnect)
      }, backoffInterval)
    },

    // 心跳检测
    startHeartbeat() {
      this.stopHeartbeat() // 确保没有重复的心跳

      this.heartbeatInterval = setInterval(() => {
        if (this.isConnected && this.socket) {
          const pingTime = Date.now()
          this.socket.send(JSON.stringify({
            type: 'ping',
            timestamp: pingTime
          }))
          this.connectionHealth.lastPing = pingTime
        }
      }, 30000) // 每30秒发送一次心跳
    },

    stopHeartbeat() {
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
      }
    },

    handlePong(data) {
      const now = Date.now()
      const latency = now - data.timestamp
      this.connectionHealth.latency = latency
      this.connectionHealth.isHealthy = latency < 5000 // 5秒内响应认为健康
      console.log(`WebSocket latency: ${latency}ms`)
    },

    handleConnectionStatus(data) {
      console.log('Received connection status:', data)

      // 更新后端连接状态
      if (data.backend_status) {
        this.connectionStates.backend = data.backend_status
      }

      // 更新连接健康状态
      if (data.health) {
        this.connectionHealth = { ...this.connectionHealth, ...data.health }
      }
    },

    // 手动重连
    async manualReconnect() {
      console.log('🔄 手动重连触发')

      // 保存当前的session_id，因为disconnect会重置它
      let savedSessionId = this.currentSessionId
      if (!savedSessionId) {
        savedSessionId = localStorage.getItem('session_id')
      }

      // 如果还是没有session_id，尝试从auth store获取
      if (!savedSessionId) {
        const authStore = useAuthStore()
        savedSessionId = authStore.sessionId
      }

      console.log('💾 保存的session_id:', savedSessionId)

      if (!savedSessionId) {
        console.error('❌ 无法获取session_id，手动重连失败')
        this.connectionError = '无法获取session_id，请重新登录'
        return
      }

      this.reconnectAttempts = 0
      try {
        this.isReconnecting = true
      } catch (error) {
        console.warn('设置isReconnecting时出错:', error)
        return
      }
      this.connectionError = null

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }

      // 先断开现有连接
      this.disconnect()

      // 等待一秒后重连
      setTimeout(async () => {
        try {
          console.log('🔌 开始重新连接...')
          console.log('🔑 使用session_id:', savedSessionId)

          // 确保connect方法是异步调用
          if (this.connect && typeof this.connect === 'function') {
            await this.connect(savedSessionId)
            console.log('✅ 手动重连完成')
          } else {
            throw new Error('connect方法不可用')
          }
        } catch (error) {
          console.error('❌ 手动重连失败:', error)
          this.connectionError = `重连失败: ${error.message || '未知错误'}`
        } finally {
          try {
            this.isReconnecting = false
          } catch (error) {
            console.warn('设置isReconnecting时出错:', error)
          }
        }
      }, 1000)
    },

    // TODO: [WebSocket交互] Client端到前端的消息处理 - 消息格式: {"message_type": "add|update", "comment_id": "string", "comment_content": "string", "comment_status": "string", "comment_intent": "string", "reply_content": "string", "process_result": "string", "timestamp": "ISO格式时间戳"} - 处理逻辑: 根据message_type判断是新增还是更新弹幕
    handleMessage(data) {
      try {
        console.log('📨 收到WebSocket消息:', {
          type: data.type || data.message_type,
          hasMessageType: !!data.message_type,
          hasType: !!data.type,
          dataKeys: Object.keys(data),
          questionId: data.question_id || data.comment_id,
          content: data.content || data.comment_content,
          status: data.status || data.comment_status,
          voiceStatus: data.voice_file_status
        })

        // 处理新的统一消息格式（DanmuMessage格式）
        if (data.message_type) {
          console.log('🎯 检测到DanmuMessage格式，使用统一处理器')
          this.handleUnifiedMessage(data)
          return
        }

        // 兼容旧的消息格式
        console.log('🔄 使用兼容模式处理消息')
        switch (data.type) {
          case 'comment':
            console.log('📝 处理弹幕消息:', data.payload)
            this.addComment(data.payload)
            break
          case 'comment_update':
            console.log('🔄 更新弹幕消息:', data.payload)
            this.updateComment(data.payload)
            break
          case 'monitoring_response':
            console.log('📊 监控响应:', data)
            if (data.success) {
              this.connectionStates.backend = 'connected'
              this.connectionHealth.isHealthy = true
            }
            break
          case 'status':
            console.log('📈 状态更新:', data.payload)
            break
          default:
            console.log('❓ 未知消息类型:', data.type, data)
            // 尝试作为DanmuMessage处理
            if (data.content || data.question_id || data.intent) {
              console.log('🔄 尝试将未知消息作为DanmuMessage处理')
              const danmuData = { ...data, message_type: 'add' }
              this.handleUnifiedMessage(danmuData)
            }
        }
      } catch (error) {
        console.error('❌ 处理WebSocket消息时发生错误:', error, data)
      }
    },

    // TODO: [WebSocket交互] 统一消息格式处理器 - 消息格式: {"message_type": "add|update", "comment_id": "string", "comment_content": "string", "comment_status": "string", "comment_intent": "string", "reply_content": "string", "process_result": "string", "timestamp": "ISO格式时间戳"} - 处理逻辑: 根据message_type执行新增或更新操作，自动补充缺失字段
    handleUnifiedMessage(data) {
      try {
        console.log('📨 处理统一格式消息:', {
          message_type: data.message_type,
          hasContent: !!data.content,
          hasQuestionId: !!data.question_id,
          dataKeys: Object.keys(data),
          data: data
        })

        // 补充缺失的字段
        const normalizedData = this.normalizeMessageData(data)
        console.log('🔧 标准化后的数据:', normalizedData)

        switch (data.message_type) {
          case 'add':
            console.log('➕ 新增弹幕:', normalizedData)
            this.addCommentFromUnified(normalizedData)
            // 更新弹幕计数
            this.updateCommentCount()
            break
          case 'update':
            console.log('🔄 更新弹幕:', normalizedData)
            this.updateCommentFromUnified(normalizedData)
            break
          default:
            console.log('❓ 未知统一消息类型:', data.message_type, data)
            // 默认作为新增处理
            console.log('🔄 默认作为新增弹幕处理')
            const defaultData = { ...normalizedData, message_type: 'add' }
            this.addCommentFromUnified(defaultData)
        }
      } catch (error) {
        console.error('❌ 处理统一格式消息时发生错误:', error, data)
      }
    },

    // TODO: [WebSocket交互] 消息数据标准化 - 处理逻辑: 将DanmuMessage格式转换为前端标准格式，自动补充缺失字段
    normalizeMessageData(data) {
      try {
        // 支持新的DanmuMessage格式
        const normalized = {
          // 基础字段映射
          comment_id: data.question_id || data.comment_id || `comment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          comment_content: data.content || data.comment_content || '',
          comment_status: data.status || data.comment_status || 'pending',
          comment_intent: data.intent || data.comment_intent || null,
          reply_content: data.reply || data.reply_content || null,
          method_type: data.method_type || null,
          user: data.user || null,
          user_game: data.user_game || null,
          msg_type: data.msg_type || data.message_type || 'add',
          timestamp: data.timestamp || new Date().toISOString(),

          // 保留其他可能的字段
          ...data
        }

        console.log('🔧 标准化DanmuMessage数据:', {
          original: data,
          normalized: normalized,
          mappings: {
            comment_id: `${data.question_id || data.comment_id} -> ${normalized.comment_id}`,
            content: `${data.content || data.comment_content} -> ${normalized.comment_content}`,
            status: `${data.status || data.comment_status} -> ${normalized.comment_status}`
          }
        })
        return normalized
      } catch (error) {
        console.error('❌ 标准化消息数据时发生错误:', error, data)
        // 返回基础的标准化数据
        return {
          comment_id: `comment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          comment_content: data.content || data.comment_content || '未知内容',
          comment_status: 'pending',
          comment_intent: null,
          reply_content: null,
          method_type: null,
          user: null,
          user_game: null,
          msg_type: 'add',
          timestamp: new Date().toISOString(),
          ...data
        }
      }
    },

    // TODO: [WebSocket交互] 从统一格式新增弹幕 - 处理逻辑: 将DanmuMessage格式转换为内部弹幕对象格式并添加到列表
    addCommentFromUnified(data) {
      try {
        console.log('➕ 开始添加新弹幕:', data.comment_id)

        // 验证弹幕数据的有效性
        if (!data.comment_id || !data.comment_content) {
          console.warn('⚠️ 弹幕数据无效，跳过添加:', data)
          return
        }

        // 过滤掉空内容或测试数据
        if (data.comment_content.trim() === '' ||
            data.comment_content === 'test' ||
            data.comment_content === '测试') {
          console.warn('⚠️ 跳过空内容或测试弹幕:', data.comment_content)
          return
        }

        // 检查是否已存在相同ID的弹幕
        const existingIndex = this.comments.findIndex(c => c.id === data.comment_id)
        if (existingIndex !== -1) {
          console.log('⚠️ 弹幕ID已存在，改为更新:', data.comment_id)
          this.updateCommentFromUnified(data)
          return
        }

        const comment = {
          // 基础标识字段
          id: data.comment_id,
          content: data.comment_content,

          // DanmuMessage核心字段
          intent: data.comment_intent,
          status: data.comment_status,
          method_type: data.method_type,
          reply: data.reply_content,
          result: data.process_result,
          user: data.user && data.user.trim() !== '' ? data.user : '用户' + Math.floor(Math.random() * 1000),
          user_game: data.user_game,

          // 语音文件相关字段
          voice_file_name: data.voice_file_name,
          voice_file_status: data.voice_file_status,

          // 时间和处理信息
          timestamp: data.timestamp,
          platform: 'unified',

          // 保留其他可能的字段以确保兼容性
          ...data
        }

        // 添加到弹幕列表末尾，保持时间顺序（最新弹幕在底部）
        this.comments.push(comment)
        this.lastCommentTime = Date.now()

        // 如果弹幕列表过长，移除最旧的弹幕（从开头移除）
        const maxComments = 100
        if (this.comments.length > maxComments) {
          this.comments = this.comments.slice(-maxComments)
        }

        console.log('✅ DanmuMessage格式弹幕已添加:', {
          id: comment.id,
          content: comment.content,
          status: comment.status,
          voice_file_status: comment.voice_file_status,
          totalComments: this.comments.length
        })

        // 移除自动选中逻辑，保持自动滚动功能正常工作
        // 只有用户手动点击弹幕时才触发选中状态
        console.log('📜 新弹幕已添加，保持自动滚动功能')
      } catch (error) {
        console.error('❌ 添加弹幕时发生错误:', error, data)
      }
    },

    // 更新弹幕计数
    updateCommentCount() {
      // 这里可以添加弹幕计数相关的逻辑
      console.log(`📊 当前弹幕总数: ${this.comments.length}`)
    },

    // TODO: [WebSocket交互] 从统一格式更新弹幕 - 处理逻辑: 根据question_id查找并更新现有弹幕信息，支持实时UI同步
    updateCommentFromUnified(data) {
      console.log('🔄 开始更新弹幕:', data.comment_id)

      const index = this.comments.findIndex(c => c.id === data.comment_id)
      if (index !== -1) {
        const existingComment = this.comments[index]
        console.log('📝 找到现有弹幕，准备更新:', {
          id: existingComment.id,
          oldStatus: existingComment.status,
          newStatus: data.comment_status,
          oldVoiceStatus: existingComment.voice_file_status,
          newVoiceStatus: data.voice_file_status
        })

        const updatedComment = {
          ...existingComment,
          // 更新DanmuMessage核心字段
          content: data.comment_content || existingComment.content,
          status: data.comment_status || existingComment.status,
          intent: data.comment_intent || existingComment.intent,
          method_type: data.method_type || existingComment.method_type,
          reply: data.reply_content || existingComment.reply,
          result: data.process_result || existingComment.result,
          user: data.user || existingComment.user,
          user_game: data.user_game || existingComment.user_game,
          timestamp: data.timestamp || existingComment.timestamp,

          // 更新语音文件相关字段
          voice_file_name: data.voice_file_name || existingComment.voice_file_name,
          voice_file_status: data.voice_file_status || existingComment.voice_file_status,

          // 保留其他字段
          ...data
        }

        this.comments[index] = updatedComment
        console.log('✅ 弹幕列表已更新:', updatedComment.id)

        // 如果更新的弹幕正处于选中状态，需要触发响应式更新
        if (this.selectedCommentId === data.comment_id) {
          console.log('🎯 当前选中弹幕正在更新，触发响应式更新')
          // 由于selectedComment是getter，它会自动获取最新的数据
          // 但我们需要确保Vue能检测到变化
          this.$patch((state) => {
            // 触发响应式更新
            state.selectedCommentId = state.selectedCommentId
          })
          console.log('🔄 选中弹幕信息已同步更新')
        }

        console.log('✅ DanmuMessage格式弹幕已更新:', {
          id: updatedComment.id,
          status: updatedComment.status,
          voice_file_status: updatedComment.voice_file_status,
          reply: updatedComment.reply ? '已生成' : '未生成'
        })
      } else {
        console.warn('⚠️ 未找到要更新的弹幕:', data.comment_id)
        // 如果找不到要更新的弹幕，可能是新弹幕，尝试添加
        console.log('🔄 尝试将更新消息作为新弹幕添加')
        this.addCommentFromUnified(data)
      }
    },

    // 移除重复的addComment方法，保留更完整的版本在文件末尾

    updateComment(updatedComment) {
      const index = this.comments.findIndex(c => c.id === updatedComment.id)
      if (index !== -1) {
        this.comments[index] = { ...this.comments[index], ...updatedComment }
      }
    },

    // 移除重复的startMonitoring和stopMonitoring方法，保留更完整的版本在文件后面

    stopCommentGeneration() {
      if (this.commentGenerationTimer) {
        clearTimeout(this.commentGenerationTimer)
        this.commentGenerationTimer = null
      }
    },

    // 移除模拟弹幕生成功能，只处理真实的WebSocket消息
    generateRealisticComments() {
      console.log('📝 模拟弹幕生成功能已禁用，只处理真实WebSocket消息')
      // 不再生成模拟弹幕，确保只显示真实的用户弹幕
    },

    generateReply(content, category) {
      const replies = {
        praise: ['谢谢支持！', '感谢夸奖', '大家太客气了', '继续努力'],
        questions: ['等下演示给大家看', '这个确实有技巧', '稍后详细说明', '好问题'],
        interaction: ['欢迎新朋友！', '感谢关注', '大家好', '谢谢支持'],
        technical: ['设备还可以', '会继续优化', '谢谢提醒', '技术问题稍后处理'],
        gaming: ['这局有点难', '确实需要小心', '运气成分', '下把换个策略'],
        gifts: ['感谢老板！', '谢谢礼物', '太客气了', '大家支持就够了']
      }

      const categoryReplies = replies[category] || replies.interaction
      return categoryReplies[Math.floor(Math.random() * categoryReplies.length)]
    },

    simulateProcessingFlow(comment) {
      // Simulate analyzing phase
      if (comment.status === 'processing') {
        setTimeout(() => {
          this.updateComment({
            id: comment.id,
            status: 'analyzing'
          })
        }, Math.random() * 1000 + 500)
      }

      // Simulate completion or failure
      const finalDelay = Math.random() * 3000 + 1500 // 1.5-4.5 seconds
      setTimeout(() => {
        const success = Math.random() > 0.1 // 90% success rate

        if (success) {
          this.updateComment({
            id: comment.id,
            status: 'completed',
            reply: this.generateReply(comment.content, comment.category),
            processing_time: Math.random() * 2 + 0.8
          })
        } else {
          this.updateComment({
            id: comment.id,
            status: 'failed',
            processing_time: Math.random() * 1 + 0.5
          })
        }
      }, finalDelay)
    },

    // ==================== 弹幕监控控制功能 ====================

    // TODO: [WebSocket-Vue→Client] 开始弹幕监控
    // 消息类型: monitoring
    // 数据格式: {"type": "monitoring", "action": "start", "params": {"process_name": string}}
    // 触发条件: 用户点击"开始弹幕监控"按钮
    // 期望响应: {"type": "monitoring_response", "success": boolean, "message": string}
    async startMonitoring(processName) {
      try {
        console.log('Starting monitoring for process:', processName)

        // 检查是否在pywebview环境中
        if (typeof window !== 'undefined' && window.pywebview && window.pywebview.api) {
          // 调用Python后端API
          const result = await window.pywebview.api.start_monitoring(processName)

          if (result.success) {
            this.isMonitoring = true
            this.monitoringProcess = processName
            this.connectionError = null

            // 更新连接状态 - 弹幕监控启动表示后端连接正常
            this.connectionStates.backend = 'connected'
            this.connectionHealth.isHealthy = true

            // 确保WebSocket连接已建立
            if (!this.isConnected) {
              console.log('🔌 WebSocket未连接，尝试建立连接...')
              try {
                await this.connect(this.currentSessionId)
                // 连接成功后更新状态
                if (this.isConnected) {
                  this.connectionStates.frontend = 'connected'
                  console.log('✅ WebSocket连接已建立')
                } else {
                  console.warn('⚠️ WebSocket连接建立失败，但监控已启动')
                }
              } catch (connectError) {
                console.error('❌ WebSocket连接失败:', connectError)
                // 即使WebSocket连接失败，监控仍可能正常工作（通过其他方式）
              }
            } else {
              // 如果已连接，确保前端连接状态正确
              this.connectionStates.frontend = 'connected'
              console.log('✅ WebSocket已连接，状态已更新')
            }

            console.log('✓ Monitoring started successfully:', result.message)
            console.log('📊 连接状态更新:', {
              frontend: this.connectionStates.frontend,
              backend: this.connectionStates.backend,
              isMonitoring: this.isMonitoring
            })
            return { success: true, message: result.message }
          } else {
            console.error('❌ Failed to start monitoring:', result.message)
            return { success: false, message: result.message }
          }
        } else {
          // 开发环境下使用mock模式
          this.isMonitoring = true
          this.monitoringProcess = processName

          // 更新连接状态 - mock模式下模拟连接成功
          this.connectionStates.frontend = 'connected'
          this.connectionStates.backend = 'connected'
          this.connectionHealth.isHealthy = true
          this.isConnected = true

          // Mock模式下不生成模拟弹幕，等待真实WebSocket消息
          console.log('📝 Mock模式已启动，等待真实WebSocket消息...')

          console.log('✓ Mock monitoring started for:', processName)
          console.log('📊 Mock连接状态更新:', {
            frontend: this.connectionStates.frontend,
            backend: this.connectionStates.backend,
            isMonitoring: this.isMonitoring
          })
          return { success: true, message: `模拟监控已启动 - 进程: ${processName}` }
        }
      } catch (error) {
        console.error('Error starting monitoring:', error)
        this.connectionError = error.message
        return { success: false, message: `启动监控失败: ${error.message}` }
      }
    },

    // TODO: [WebSocket-Vue→Client] 停止弹幕监控
    // 消息类型: monitoring
    // 数据格式: {"type": "monitoring", "action": "stop"}
    // 触发条件: 用户点击"停止弹幕监控"按钮
    // 期望响应: {"type": "monitoring_response", "success": boolean, "message": string}
    async stopMonitoring() {
      try {
        console.log('🛑 Stopping monitoring...')

        // 检查是否在pywebview环境中
        if (typeof window !== 'undefined' && window.pywebview && window.pywebview.api) {
          // 调用Python后端API
          console.log('📡 Calling backend API: stop_monitoring()')
          const result = await window.pywebview.api.stop_monitoring()
          console.log('📡 Backend response:', result)

          if (result.success) {
            // 立即更新前端状态
            this.isMonitoring = false
            this.monitoringProcess = null

            // 清除任何前端定时器
            if (this.commentGenerationTimer) {
              clearInterval(this.commentGenerationTimer)
              this.commentGenerationTimer = null
            }

            // 停止接收新的弹幕数据
            this.stopReceivingComments()

            console.log('✅ Monitoring stopped successfully:', result.message)
            return { success: true, message: result.message }
          } else {
            console.error('❌ Failed to stop monitoring:', result.message)
            return { success: false, message: result.message }
          }
        } else {
          // 开发环境下停止mock模式
          this.isMonitoring = false
          this.monitoringProcess = null

          // 清除定时器
          if (this.commentGenerationTimer) {
            clearInterval(this.commentGenerationTimer)
            this.commentGenerationTimer = null
          }

          // 停止mock数据生成
          this.stopCommentGeneration()

          console.log('✅ Mock monitoring stopped')
          return { success: true, message: '弹幕监控已停止' }
        }
      } catch (error) {
        console.error('💥 Error stopping monitoring:', error)
        this.connectionError = error.message
        return { success: false, message: `停止监控失败: ${error.message}` }
      }
    },

    // 停止接收新的弹幕数据
    stopReceivingComments() {
      console.log('🚫 Stopping comment reception...')
      // 这里可以添加额外的清理逻辑
      // 比如清除WebSocket消息监听器等
    },

    // TODO: [WebSocket-Vue→Client] 配置自动化设置
    // 消息类型: automation
    // 数据格式: {"type": "automation", "action": "configure", "config": {"enabled": boolean, "rules": array}}
    // 触发条件: 用户修改自动化配置设置
    // 期望响应: {"type": "automation_response", "success": boolean, "message": string}
    async configureAutomation(config) {
      try {
        console.log('⚙️ Configuring automation...', config)

        if (!this.isConnected || !this.socket) {
          console.warn('WebSocket未连接，无法配置自动化')
          return { success: false, message: 'WebSocket未连接' }
        }

        const message = {
          type: 'automation',
          action: 'configure',
          config: config,
          timestamp: Date.now()
        }

        this.socket.send(JSON.stringify(message))
        console.log('✅ 自动化配置消息已发送')
        return { success: true, message: '自动化配置已发送' }
      } catch (error) {
        console.error('❌ 配置自动化失败:', error)
        return { success: false, message: `配置失败: ${error.message}` }
      }
    },

    // ==================== 智能滚动控制功能 ====================

    selectComment(commentOrId) {
      // 支持传入comment对象或commentId
      const commentId = typeof commentOrId === 'object' ? commentOrId.id : commentOrId
      this.selectedCommentId = commentId
      this.autoScroll = false
      this.showScrollToBottom = true
      console.log('🎯 弹幕已选中，自动滚动已禁用:', commentId)

      // 如果传入的是comment对象，记录详细信息
      if (typeof commentOrId === 'object') {
        console.log('📝 选中弹幕详细信息:', {
          id: commentOrId.id,
          content: commentOrId.content,
          status: commentOrId.status,
          intent: commentOrId.intent,
          method_type: commentOrId.method_type,
          voice_file_name: commentOrId.voice_file_name,
          voice_file_status: commentOrId.voice_file_status
        })
      }
    },

    unselectComment() {
      this.selectedCommentId = null
      // 不自动恢复滚动，需要用户手动点击按钮
    },

    scrollToBottom() {
      this.autoScroll = true
      this.showScrollToBottom = false
      this.selectedCommentId = null
      console.log('Scrolled to bottom, auto-scroll enabled')
    },

    checkScrollPosition(isAtBottom) {
      if (!isAtBottom && this.autoScroll) {
        // 用户手动滚动离开底部
        this.autoScroll = false
        this.showScrollToBottom = true
        console.log('User scrolled away from bottom, showing scroll button')
      }
    },

    // 添加新弹幕时的处理
    addComment(comment) {
      // 为弹幕添加唯一ID和时间戳
      const newComment = {
        ...comment,
        id: comment.id || Date.now() + Math.random(),
        timestamp: comment.timestamp || new Date().toISOString()
      }

      console.log('📌 添加新弹幕:', newComment)

      // 检查是否在监控状态
      if (!this.isMonitoring) {
        console.warn('⚠️ 收到弹幕但未处于监控状态，可能需要先启动监控')
      }

      this.comments.push(newComment)
      this.lastCommentTime = newComment.timestamp

      // 保持最多100条评论以防止内存问题
      if (this.comments.length > 100) {
        this.comments = this.comments.slice(-100)
      }

      // 如果启用了自动滚动，新弹幕到达时应该滚动到底部
      if (this.autoScroll) {
        // 触发滚动事件（由组件监听）
        this.$nextTick && this.$nextTick(() => {
          // 组件会监听comments变化并自动滚动
        })
      }

      console.log(`✅ 弹幕已添加 (总数: ${this.comments.length}): ${newComment.content}`)
    },

    // TODO: [WebSocket-Vue→Client] 发送弹幕消息到Client端
    // 消息类型: comment
    // 数据格式: {"type": "comment", "payload": comment, "timestamp": int}
    // 触发条件: 用户点击弹幕监控或发送弹幕
    // 期望响应: Client端处理并广播comment_update消息
    sendCommentMessage(comment) {
      if (!this.isConnected || !this.socket) {
        console.warn('WebSocket未连接，无法发送弹幕消息')
        return false
      }

      try {
        const message = {
          type: 'comment',
          payload: comment,
          timestamp: Date.now()
        }

        this.socket.send(JSON.stringify(message))
        console.log('✅ 弹幕消息已发送到Client端:', comment.content)
        return true
      } catch (error) {
        console.error('❌ 发送弹幕消息失败:', error)
        return false
      }
    },

    // 测试WebSocket连接
    testWebSocketConnection() {
      if (!this.isConnected || !this.socket) {
        console.warn('WebSocket未连接')
        return false
      }

      try {
        const testMessage = {
          type: 'ping',
          timestamp: Date.now()
        }

        this.socket.send(JSON.stringify(testMessage))
        console.log('✅ 测试消息已发送')
        return true
      } catch (error) {
        console.error('❌ 发送测试消息失败:', error)
        return false
      }
    },

    // 清理所有数据，用于退出登录时重置状态
    clearAllData() {
      console.log('🧹 清理WebSocket store所有数据...')

      // 清空弹幕评论
      this.comments = []
      this.lastCommentTime = null

      // 重置监控状态
      this.isMonitoring = false
      this.monitoringProcess = null

      // 重置连接状态
      this.connectionStates.frontend = 'disconnected'
      this.connectionStates.backend = 'disconnected'
      this.connectionHealth.isHealthy = false
      this.connectionHealth.lastPing = null
      this.connectionHealth.latency = null

      // 清除会话信息
      this.currentSessionId = null
      this.connectionStartTime = null
      this.connectionError = null

      // 重置重连状态
      this.reconnectAttempts = 0
      try {
        this.isReconnecting = false
      } catch (error) {
        console.warn('设置isReconnecting时出错:', error)
      }

      // 清除定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }

      if (this.mockConnectionTimer) {
        clearTimeout(this.mockConnectionTimer)
        this.mockConnectionTimer = null
      }

      // 停止mock数据生成
      this.stopCommentGeneration()

      console.log('✅ WebSocket store数据已清理')
    }
  }
})
