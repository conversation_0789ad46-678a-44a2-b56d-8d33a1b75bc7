<template>
  <el-dialog
    v-model="visible"
    title="配置设置"
    width="600px"
    :before-close="handleClose"
    class="config-modal"
  >
    <div class="config-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- Path Settings Tab -->
        <el-tab-pane label="路径设置" name="paths">
          <div class="tab-content">
            <el-form
              ref="pathFormRef"
              :model="pathConfig"
              :rules="pathRules"
              label-width="200px"
              label-position="left"
            >
              <el-form-item label="截图存储路径：" prop="screenshot">
                <el-input
                  v-model="pathConfig.screenshot"
                  placeholder="/tmp/screenshots"
                >
                  <template #append>
                    <el-button :icon="FolderOpened" @click="selectPath('screenshot')">
                      浏览
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="模型存储路径：" prop="model">
                <el-input
                  v-model="pathConfig.model"
                  placeholder="/models/recognition"
                >
                  <template #append>
                    <el-button :icon="FolderOpened" @click="selectPath('model')">
                      浏览
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- Hotkey Settings Tab -->
        <el-tab-pane label="快捷键设置" name="hotkeys">
          <div class="tab-content">
            <el-form
              ref="hotkeyFormRef"
              :model="hotkeyConfig"
              label-width="200px"
              label-position="left"
            >
              <el-form-item label="监控切换：">
                <div class="hotkey-input">
                  <el-input
                    v-model="hotkeyConfig.monitoring"
                    readonly
                    placeholder="点击设置快捷键"
                    @click="startHotkeyCapture('monitoring')"
                    :class="{ 'capturing': capturingHotkey === 'monitoring' }"
                  >
                    <template #suffix>
                      <el-icon v-if="capturingHotkey === 'monitoring'" class="capturing-icon">
                        <Loading />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-button
                    type="danger"
                    :icon="Delete"
                    @click="clearHotkey('monitoring')"
                    :disabled="!hotkeyConfig.monitoring"
                  >
                    清除
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="自动化切换：">
                <div class="hotkey-input">
                  <el-input
                    v-model="hotkeyConfig.automation"
                    readonly
                    placeholder="点击设置快捷键"
                    @click="startHotkeyCapture('automation')"
                    :class="{ 'capturing': capturingHotkey === 'automation' }"
                  >
                    <template #suffix>
                      <el-icon v-if="capturingHotkey === 'automation'" class="capturing-icon">
                        <Loading />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-button
                    type="danger"
                    :icon="Delete"
                    @click="clearHotkey('automation')"
                    :disabled="!hotkeyConfig.automation"
                  >
                    清除
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
            
            <div class="hotkey-instructions">
              <el-alert
                title="快捷键说明"
                type="info"
                :closable="false"
                show-icon
              >
                <p>点击输入框并按下您想要使用的组合键。</p>
                <p>支持的修饰键：Ctrl、Alt、Shift、Meta（Cmd/Win）</p>
                <p>示例：Ctrl+F1、Alt+Shift+M 等。</p>
              </el-alert>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="handleSave"
        >
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'
import { configAPI } from '@/api'
import { ElMessage } from 'element-plus'
import { FolderOpened, Delete, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const activeTab = ref('paths')
const saveLoading = ref(false)
const capturingHotkey = ref(null)

const pathFormRef = ref()
const hotkeyFormRef = ref()

const pathConfig = reactive({
  screenshot: '',
  model: ''
})

const hotkeyConfig = reactive({
  monitoring: '',
  automation: ''
})

const pathRules = {
  screenshot: [
    { required: true, message: '截图存储路径为必填项', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '模型存储路径为必填项', trigger: 'blur' }
  ]
}

// Watch for prop changes
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadConfiguration()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const loadConfiguration = async () => {
  try {
    const response = await configAPI.getConfig()
    const config = response.data
    
    // Load path configuration
    if (config.paths) {
      pathConfig.screenshot = config.paths.screenshot || ''
      pathConfig.model = config.paths.model || ''
    }
    
    // Load hotkey configuration
    if (config.hotkeys) {
      hotkeyConfig.monitoring = config.hotkeys.monitoring || ''
      hotkeyConfig.automation = config.hotkeys.automation || ''
    }
  } catch (error) {
    console.error('Failed to load configuration:', error)
    ElMessage.error('加载配置失败')
  }
}

const selectPath = (type) => {
  // In a real application, this would open a file dialog
  // For now, we'll show a message
  ElMessage.info('在实际应用中这里会打开文件对话框')
}

const startHotkeyCapture = (type) => {
  capturingHotkey.value = type
  const typeMap = { monitoring: '监控', automation: '自动化' }
  ElMessage.info(`请按下${typeMap[type]}快捷键组合`)
}

const clearHotkey = (type) => {
  hotkeyConfig[type] = ''
}

const handleKeyDown = (event) => {
  if (!capturingHotkey.value) return
  
  event.preventDefault()
  event.stopPropagation()
  
  const modifiers = []
  if (event.ctrlKey) modifiers.push('Ctrl')
  if (event.altKey) modifiers.push('Alt')
  if (event.shiftKey) modifiers.push('Shift')
  if (event.metaKey) modifiers.push('Meta')
  
  let key = event.key
  
  // Handle special keys
  if (key === ' ') key = 'Space'
  if (key === 'Control' || key === 'Alt' || key === 'Shift' || key === 'Meta') {
    return // Don't capture modifier keys alone
  }
  
  // Convert to uppercase for function keys
  if (key.startsWith('F') && key.length <= 3) {
    key = key.toUpperCase()
  }
  
  const hotkey = modifiers.length > 0 ? `${modifiers.join('+')}+${key}` : key
  hotkeyConfig[capturingHotkey.value] = hotkey
  capturingHotkey.value = null

  ElMessage.success(`快捷键已设置为：${hotkey}`)
}

const handleSave = async () => {
  try {
    // Validate forms
    const pathValid = await pathFormRef.value?.validate()
    if (!pathValid) {
      activeTab.value = 'paths'
      return
    }
    
    saveLoading.value = true
    
    const config = {
      paths: {
        screenshot: pathConfig.screenshot,
        model: pathConfig.model
      },
      hotkeys: {
        monitoring: hotkeyConfig.monitoring,
        automation: hotkeyConfig.automation
      }
    }
    
    const response = await configAPI.updateConfig(config)
    
    if (response.data.success) {
      ElMessage.success('配置保存成功')
      handleClose()
    }
  } catch (error) {
    console.error('Failed to save configuration:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saveLoading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  capturingHotkey.value = null
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.config-content {
  min-height: 400px;
}

.tab-content {
  padding: 20px;
}

.hotkey-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.hotkey-input .el-input {
  flex: 1;
}

.capturing {
  border-color: #5865f2 !important;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2) !important;
}

.capturing-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hotkey-instructions {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus customizations */
:deep(.el-dialog) {
  background: rgba(45, 45, 68, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

:deep(.el-dialog__header) {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px;
}

:deep(.el-dialog__title) {
  color: #ffffff;
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-tabs--border-card) {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-tabs__header) {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-tabs__item) {
  color: #c0c0c0;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-tabs__item.is-active) {
  color: #5865f2;
  background: rgba(88, 101, 242, 0.1);
}

:deep(.el-tabs__content) {
  background: transparent;
}

:deep(.el-form-item__label) {
  color: #c0c0c0;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-input__inner) {
  color: #ffffff;
}

:deep(.el-alert) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-alert__title) {
  color: #ffffff;
}

:deep(.el-alert__description) {
  color: #c0c0c0;
}
</style>
