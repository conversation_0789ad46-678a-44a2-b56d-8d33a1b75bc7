import axios from 'axios'

// Check if running in pywebview environment
const isInPyWebViewEnvironment = () => {
  return typeof window !== 'undefined' && window.pywebview && window.pywebview.api
}

// API Configuration - Use real API in pywebview, mock in development
const getAPIBaseURL = () => {
  if (isInPyWebViewEnvironment()) {
    // In pywebview environment, use real server API
    return 'http://localhost:8000'
  } else {
    // In development environment, use mock
    return 'mock://api'
  }
}

// Create axios instance with dynamic config
const apiClient = axios.create({
  baseURL: getAPIBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // 动态更新baseURL以确保运行时环境检测正确
    const currentBaseURL = getAPIBaseURL()
    if (config.baseURL !== currentBaseURL) {
      config.baseURL = currentBaseURL
      console.log(`🔄 API baseURL updated to: ${currentBaseURL}`)
    }

    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Use the same function for consistency
const isInPyWebView = isInPyWebViewEnvironment

// API Functions with pywebview integration
export const authAPI = {
  async login(loginData) {
    if (isInPyWebView()) {
      // Use real pywebview API
      try {
        const result = await window.pywebview.api.login(loginData)
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '登录失败' }
          }
        }
      }
    } else {
      // Fallback to mock mode for development
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const { username, password, tenantId } = loginData

          if (!username || username.trim().length < 2) {
            reject({
              response: {
                data: { message: '用户名至少需要2个字符' }
              }
            })
            return
          }

          if (!password || password.trim().length < 6) {
            reject({
              response: {
                data: { message: '密码至少需要6个字符' }
              }
            })
            return
          }

          if (!tenantId || tenantId.trim().length === 0) {
            reject({
              response: {
                data: { message: '请选择租户' }
              }
            })
            return
          }

          const mockUser = {
            id: Math.floor(Math.random() * 10000),
            username: username.trim(),
            tenantId: tenantId.trim(),
            joinTime: new Date().toISOString(),
            permissions: ['monitor', 'automation', 'export']
          }

          const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

          // Mock游戏和音色数据
          const mockGameList = [
            { gameId: 'game001', gameName: '王者荣耀' },
            { gameId: 'game002', gameName: '英雄联盟' },
            { gameId: 'game003', gameName: '原神' }
          ]

          const mockVoiceList = [
            { voiceId: 'voice001', voiceName: '甜美女声', gameId: 'game001', genderId: 'female' },
            { voiceId: 'voice002', voiceName: '磁性男声', gameId: 'game001', genderId: 'male' },
            { voiceId: 'voice003', voiceName: '活泼女声', gameId: 'game002', genderId: 'female' },
            { voiceId: 'voice004', voiceName: '沉稳男声', gameId: 'game002', genderId: 'male' },
            { voiceId: 'voice005', voiceName: '温柔女声', gameId: 'game003', genderId: 'female' },
            { voiceId: 'voice006', voiceName: '阳光男声', gameId: 'game003', genderId: 'male' }
          ]

          resolve({
            data: {
              success: true,
              token: mockToken,
              user: mockUser,
              gameList: mockGameList,
              voiceList: mockVoiceList,
              message: '登录成功'
            }
          })
        }, Math.random() * 800 + 200)
      })
    }
  },

  async logout() {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.logout()
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '登出失败' }
          }
        }
      }
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ data: { success: true, message: '退出成功' } })
        }, 300)
      })
    }
  }
}

export const processAPI = {
  async startMonitoring(processName) {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.start_monitoring(processName)
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '启动监控失败' }
          }
        }
      }
    } else {
      // Fallback to mock mode
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (!processName || processName.trim().length < 2) {
            reject({
              response: {
                data: { message: '进程名称不能为空' }
              }
            })
            return
          }

          const detectedProcesses = [
            'OBS Studio', 'XSplit', 'Streamlabs OBS', 'Bandicam',
            'NVIDIA ShadowPlay', '腾讯会议', '钉钉', 'Zoom'
          ]

          const isKnownProcess = detectedProcesses.some(p =>
            processName.toLowerCase().includes(p.toLowerCase()) ||
            p.toLowerCase().includes(processName.toLowerCase())
          )

          resolve({
            data: {
              success: true,
              message: `弹幕监控已启动 - ${isKnownProcess ? '检测到' : '模拟'}进程: ${processName}`,
              processInfo: {
                name: processName,
                pid: Math.floor(Math.random() * 10000) + 1000,
                status: isKnownProcess ? 'running' : 'simulated'
              }
            }
          })
        }, Math.random() * 600 + 400)
      })
    }
  },

  async stopMonitoring() {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.stop_monitoring()
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '停止监控失败' }
          }
        }
      }
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              message: '弹幕监控已停止',
              timestamp: new Date().toISOString()
            }
          })
        }, Math.random() * 400 + 200)
      })
    }
  },

  async startAutomation(targetProcess) {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.start_automation(targetProcess)
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '启动自动化失败' }
          }
        }
      }
    } else {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (!targetProcess || targetProcess.trim().length < 2) {
            reject({
              response: {
                data: { message: '目标进程名称不能为空' }
              }
            })
            return
          }

          const automationTypes = ['键盘输入', '鼠标操作', '窗口控制', '文件操作']
          const selectedType = automationTypes[Math.floor(Math.random() * automationTypes.length)]

          resolve({
            data: {
              success: true,
              message: `自动化已启动 - 目标进程: ${targetProcess}`,
              automationInfo: {
                targetProcess,
                type: selectedType,
                pid: Math.floor(Math.random() * 10000) + 1000,
                status: 'active'
              }
            }
          })
        }, Math.random() * 800 + 400)
      })
    }
  },

  async stopAutomation() {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.stop_automation()
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '停止自动化失败' }
          }
        }
      }
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              success: true,
              message: '自动化已停止',
              timestamp: new Date().toISOString()
            }
          })
        }, Math.random() * 400 + 200)
      })
    }
  }
}

export const commentAPI = {
  async retryProcessing(commentId) {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.retry_processing(commentId)
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '重试处理失败' }
          }
        }
      }
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          const retryComment = {
            id: commentId,
            status: 'processing',
            timestamp: new Date().toISOString(),
            retry_count: Math.floor(Math.random() * 3) + 1,
            processing_time: 0
          }

          resolve({
            data: {
              success: true,
              message: '评论重新处理已启动',
              comment: retryComment
            }
          })
        }, Math.random() * 800 + 500)
      })
    }
  },

  async exportSession() {
    if (isInPyWebView()) {
      try {
        const result = await window.pywebview.api.export_session()
        return { data: result }
      } catch (error) {
        throw {
          response: {
            data: { message: error.message || '导出失败' }
          }
        }
      }
    } else {
      return new Promise((resolve) => {
        setTimeout(() => {
          const timestamp = new Date().toISOString().split('T')[0]
          const filename = `弹幕会话数据_${timestamp}_${Math.random().toString(36).substring(2, 8)}.xlsx`

          resolve({
            data: {
              success: true,
              message: '会话数据已导出到Excel文件',
              filename,
              exportInfo: {
                totalComments: Math.floor(Math.random() * 500) + 100,
                exportTime: new Date().toISOString(),
                fileSize: `${Math.floor(Math.random() * 500) + 50}KB`
              }
            }
          })
        }, Math.random() * 1500 + 1000)
      })
    }
  }
}

// 旧的configAPI已移除，使用下面新的完整版本

// WebSocket URL - Use real WebSocket in pywebview, mock in development
export const getWebSocketURL = (sessionId = null) => {
  if (isInPyWebView()) {
    // In pywebview, connect to the real WebSocket server
    // Client端的WebSocket服务器运行在localhost:10000
    // session_id会在连接建立后通过消息传递给Client端
    return 'ws://localhost:10000'
  } else {
    // In development, use mock WebSocket
    return 'mock://websocket'
  }
}

// 配置管理API
export const configAPI = {
  // 快捷键配置接口
  hotkeys: {
    // 读取快捷键配置
    get: async (roomName) => {
      if (isInPyWebView()) {
        try {
          const response = await apiClient.get(`/api/config/hotkeys/${roomName}`)
          return response
        } catch (error) {
          console.error('读取快捷键配置失败:', error)
          throw error
        }
      } else {
        // Mock模式
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: {
                success: true,
                data: {
                  monitoring: 'F1',
                  automation: 'F2'
                },
                message: '快捷键配置读取成功'
              }
            })
          }, 300)
        })
      }
    },

    // 保存快捷键配置
    save: async (roomName, config) => {
      if (isInPyWebView()) {
        try {
          const response = await apiClient.post(`/api/config/hotkeys/${roomName}`, config)
          return response
        } catch (error) {
          console.error('保存快捷键配置失败:', error)
          throw error
        }
      } else {
        // Mock模式
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: {
                success: true,
                data: config,
                message: '快捷键配置保存成功'
              }
            })
          }, 500)
        })
      }
    }
  },

  // 存储路径配置接口
  paths: {
    // 读取存储路径配置
    get: async (roomName) => {
      if (isInPyWebView()) {
        try {
          const response = await apiClient.get(`/api/config/paths/${roomName}`)
          return response
        } catch (error) {
          console.error('读取存储路径配置失败:', error)
          throw error
        }
      } else {
        // Mock模式
        return new Promise((resolve) => {
          setTimeout(() => {
            const mockPaths = {
              screenshot: '/Users/<USER>/Documents/智能主播助手/screenshot',
              voice_cache: '/Users/<USER>/Documents/智能主播助手/voice_cache',
              models: '/Users/<USER>/Documents/智能主播助手/models'
            }
            resolve({
              data: {
                success: true,
                data: mockPaths,
                message: '存储路径配置读取成功'
              }
            })
          }, 300)
        })
      }
    },

    // 保存存储路径配置
    save: async (roomName, config) => {
      if (isInPyWebView()) {
        try {
          const response = await apiClient.post(`/api/config/paths/${roomName}`, config)
          return response
        } catch (error) {
          console.error('保存存储路径配置失败:', error)
          throw error
        }
      } else {
        // Mock模式
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: {
                success: true,
                data: config,
                message: '存储路径配置保存成功'
              }
            })
          }, 500)
        })
      }
    },

    // 重置默认路径
    reset: async (roomName, basePath) => {
      if (isInPyWebView()) {
        try {
          const response = await apiClient.post(`/api/config/paths/${roomName}/reset`, null, {
            params: { base_path: basePath }
          })
          return response
        } catch (error) {
          console.error('重置默认路径失败:', error)
          throw error
        }
      } else {
        // Mock模式
        return new Promise((resolve) => {
          setTimeout(() => {
            const defaultPaths = {
              screenshot: `${basePath}/screenshot`,
              voice_cache: `${basePath}/voice_cache`,
              models: `${basePath}/models`
            }
            resolve({
              data: {
                success: true,
                data: defaultPaths,
                message: '默认路径重置成功'
              }
            })
          }, 600)
        })
      }
    }
  }
}

export default apiClient
