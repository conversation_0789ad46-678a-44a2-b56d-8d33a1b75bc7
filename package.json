{"name": "intelligent-anchor-v2-vue", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^4.5.3", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.3", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2"}}