<template>
  <div class="dashboard">
    <!-- Header -->
    <header class="dashboard-header">
      <div class="header-left">
        <div class="logo">
          <el-icon size="24" color="#5865f2">
            <VideoCamera />
          </el-icon>
          <span class="app-title">智能主播助手</span>
        </div>
        <div class="room-info">
          <el-tag type="success" size="small">
            <el-icon><User /></el-icon>
            {{ authStore.user?.roomName }}
          </el-tag>
          <!-- 连接状态文字移至直播间名称右侧 -->
          <ConnectionStatus class="room-connection-status" />
        </div>
      </div>

      <div class="header-right">
        <el-button
          type="primary"
          :icon="Download"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出会话数据
        </el-button>

        <el-button
          :icon="Setting"
          @click="showSettingsModal = true"
        >
          设置
        </el-button>

        <el-button
          type="danger"
          :icon="SwitchButton"
          @click="handleLogout"
        >
          退出登录
        </el-button>
      </div>
    </header>

    <!-- Process Configuration Section -->
    <div class="config-section">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>进程配置</span>
          </div>
        </template>
        
        <div class="config-content">
          <div class="config-row">
            <div class="config-item">
              <label>直播软件进程名称：</label>
              <el-input
                v-model="processStore.streamingProcess"
                placeholder="例如：996传奇盒子"
                :disabled="processStore.isMonitoring"
              />
            </div>

            <div class="config-item">
              <label>自动化目标进程名称：</label>
              <el-input
                v-model="processStore.targetProcess"
                placeholder="游戏区服名称"
                :disabled="processStore.isAutomating"
              />
            </div>
          </div>
          
          <div class="config-actions">
            <el-button
              :type="websocketStore.isMonitoring ? 'danger' : 'primary'"
              :disabled="!processStore.canStartMonitoring && !websocketStore.isMonitoring"
              :loading="monitoringLoading"
              @click="toggleMonitoring"
            >
              <el-icon><VideoPlay v-if="!websocketStore.isMonitoring" /><VideoPause v-else /></el-icon>
              {{ websocketStore.isMonitoring ? '停止' : '开始' }}弹幕监控
            </el-button>

            <el-button
              :type="processStore.isAutomating ? 'danger' : 'success'"
              :disabled="!processStore.canStartAutomation && !processStore.isAutomating"
              :loading="processStore.loading.automation"
              @click="toggleAutomation"
            >
              <el-icon><VideoPlay v-if="!processStore.isAutomating" /><VideoPause v-else /></el-icon>
              {{ processStore.isAutomating ? '停止' : '开始' }}自动化
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- Bullet Comment Monitoring Dashboard -->
    <div class="monitoring-section">
      <div class="monitoring-panels">
        <!-- Left Panel: Comment Feed -->
        <div class="left-panel">
          <CommentFeed @comment-selected="handleCommentSelected" />
        </div>
        
        <!-- Right Panel: Comment Details -->
        <div class="right-panel">
          <CommentDetails :selected-comment="selectedComment" />
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <SettingsModal v-model="showSettingsModal" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useProcessStore } from '@/stores/process'
import { useWebSocketStore } from '@/stores/websocket'
import { commentAPI } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoCamera, User, Download, Setting, SwitchButton, Monitor,
  VideoPlay, VideoPause
} from '@element-plus/icons-vue'

import CommentFeed from '@/components/CommentFeed.vue'
import CommentDetails from '@/components/CommentDetails.vue'
import SettingsModal from '@/components/SettingsModal.vue'
import ConnectionStatus from '@/components/ConnectionStatus.vue'

const router = useRouter()
const authStore = useAuthStore()
const processStore = useProcessStore()
const websocketStore = useWebSocketStore()

// 使用WebSocket store中的selectedComment，而不是本地状态
const selectedComment = computed(() => websocketStore.selectedComment)
const showSettingsModal = ref(false)
const exportLoading = ref(false)
const monitoringLoading = ref(false)

const handleCommentSelected = (comment) => {
  console.log('🎯 Dashboard接收到弹幕选择事件:', comment.id)
  // 不需要设置本地状态，因为CommentFeed已经调用了store的selectComment方法
  // selectedComment computed会自动获取store中的最新数据
}

const toggleMonitoring = async () => {
  try {
    const processName = processStore.streamingProcess || '996传奇盒子'

    if (websocketStore.isMonitoring) {
      // 停止监控
      monitoringLoading.value = true
      console.log('Stopping monitoring...')

      const result = await websocketStore.stopMonitoring()

      if (result.success) {
        console.log('✓ Monitoring stopped successfully')
        ElMessage.success(result.message)
      } else {
        console.error('❌ Failed to stop monitoring:', result.message)
        ElMessage.error(result.message)
      }

      monitoringLoading.value = false
    } else {
      // 开始监控
      monitoringLoading.value = true
      console.log('Starting monitoring for:', processName)

      const result = await websocketStore.startMonitoring(processName)

      if (result.success) {
        console.log('✓ Monitoring started successfully')
        ElMessage.success(result.message)
      } else {
        console.error('❌ Failed to start monitoring:', result.message)
        ElMessage.error(result.message)
      }

      monitoringLoading.value = false
    }
  } catch (error) {
    console.error('Error in toggleMonitoring:', error)
    monitoringLoading.value = false
    ElMessage.error(`操作失败: ${error.message}`)
  }
}

const toggleAutomation = async () => {
  try {
    console.log(`${processStore.isAutomating ? 'Stopping' : 'Starting'} automation...`)

    const result = await processStore.toggleAutomation()

    if (result.success) {
      const message = result.data?.message || result.message || '操作成功'
      console.log('✅ Automation toggle successful:', message)
      ElMessage.success(message)
    } else {
      const error = result.error || result.message || '操作失败'
      console.error('❌ Automation toggle failed:', error)
      ElMessage.error(error)
    }
  } catch (error) {
    console.error('💥 Error in toggleAutomation:', error)
    ElMessage.error(`操作失败: ${error.message}`)
  }
}

const handleExport = async () => {
  try {
    exportLoading.value = true
    const result = await commentAPI.exportSession()
    if (result.data.success) {
      ElMessage.success(`会话数据已导出：${result.data.filename}`)
    }
  } catch (error) {
    ElMessage.error('导出会话数据失败')
  } finally {
    exportLoading.value = false
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？这将停止所有监控和自动化功能。',
      '确认退出',
      {
        confirmButtonText: '退出',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('🚪 开始退出登录流程...')

    try {
      // 调用auth store的logout方法，它会处理所有状态清理
      console.log('🔐 调用完整的登出流程...')
      const logoutResult = await authStore.logout()

      if (logoutResult && logoutResult.success) {
        console.log('✅ 退出登录完成，跳转到登录页面')
        ElMessage.success('退出登录成功')
      } else {
        console.warn('⚠️ 登出过程中出现问题，但本地状态已清理')
        ElMessage.warning(logoutResult?.message || '退出登录时出现问题，但本地状态已清理')
      }

      // 强制跳转到登录页面
      await router.push('/login')

    } catch (logoutError) {
      console.error('❌ 退出登录过程中出现错误:', logoutError)
      ElMessage.error(`退出登录失败: ${logoutError.message || '未知错误'}`)

      // 即使出错也尝试跳转到登录页面
      try {
        await router.push('/login')
      } catch (routerError) {
        console.error('❌ 路由跳转失败:', routerError)
        // 强制刷新页面
        window.location.href = '/login'
      }
    }

  } catch (error) {
    // User cancelled or other error
    if (error !== 'cancel') {
      console.error('❌ 退出登录确认对话框错误:', error)
    }
  }
}

onMounted(() => {
  // Initialize auth state
  authStore.initializeAuth()
  
  // Ensure WebSocket connection
  if (!websocketStore.isConnected) {
    websocketStore.connect()
    websocketStore.startMockData()
  }
})

onUnmounted(() => {
  // Clean up WebSocket connection
  websocketStore.disconnect()
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: rgba(45, 45, 68, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.room-info {
  display: flex;
  align-items: center;
  gap: 16px; /* 增加间距以容纳连接状态 */
}

.room-connection-status {
  margin-left: 12px;
  /* 确保连接状态组件在直播间名称右侧正确显示 */
  flex-shrink: 0; /* 防止被压缩 */
  display: inline-flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-section {
  padding: 24px;
  flex-shrink: 0;
}

.config-card {
  background: rgba(45, 45, 68, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 600;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  color: #c0c0c0;
  font-size: 14px;
  font-weight: 500;
}

.config-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.monitoring-section {
  flex: 1;
  padding: 0 24px 24px;
  overflow: hidden;
}

.monitoring-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: 100%;
}

.left-panel,
.right-panel {
  background: rgba(45, 45, 68, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Element Plus customizations */
:deep(.el-card) {
  background: transparent;
  border: none;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

:deep(.el-input__inner) {
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: #a0a0a0;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-tag) {
  border-radius: 6px;
}
</style>
