import { defineStore } from 'pinia'

export const useHotkeysStore = defineStore('hotkeys', {
  state: () => ({
    // 快捷键配置
    hotkeys: {
      monitoring: 'F1',
      automation: 'F2',
      export: 'F3',
      screenshot: 'F4'
    },
    
    // 快捷键监听状态
    isListening: false,
    currentListeningKey: null, // 当前正在监听的快捷键类型
    
    // 按键检测状态
    pressedKeys: new Set(),
    keySequence: [],
    
    // 快捷键绑定数据（准备发送给后端）
    pendingBindings: {},
    
    // 支持的修饰键
    modifierKeys: ['Ctrl', 'Shift', 'Alt', 'Meta', 'Cmd'],
    
    // 支持的功能键
    functionKeys: [
      'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'
    ]
  }),

  getters: {
    // 获取格式化的快捷键字符串
    getFormattedHotkey: (state) => (keyType) => {
      return state.hotkeys[keyType] || '未设置'
    },
    
    // 检查是否有待绑定的快捷键
    hasPendingBindings: (state) => {
      return Object.keys(state.pendingBindings).length > 0
    },
    
    // 获取当前按下的按键组合
    getCurrentKeyCombo: (state) => {
      if (state.keySequence.length === 0) return ''
      
      const modifiers = []
      const keys = []
      
      state.keySequence.forEach(key => {
        if (state.modifierKeys.includes(key)) {
          modifiers.push(key)
        } else {
          keys.push(key)
        }
      })
      
      // 按标准顺序排列修饰键
      const orderedModifiers = ['Ctrl', 'Shift', 'Alt', 'Meta'].filter(mod => 
        modifiers.includes(mod)
      )
      
      return [...orderedModifiers, ...keys].join('+')
    }
  },

  actions: {
    // ==================== 快捷键监听控制 ====================
    
    startListening(keyType) {
      console.log(`开始监听快捷键: ${keyType}`)
      this.isListening = true
      this.currentListeningKey = keyType
      this.pressedKeys.clear()
      this.keySequence = []
      
      // 添加全局键盘事件监听器
      this.addKeyboardListeners()
    },
    
    stopListening() {
      console.log('停止监听快捷键')
      this.isListening = false
      this.currentListeningKey = null
      this.pressedKeys.clear()
      this.keySequence = []
      
      // 移除全局键盘事件监听器
      this.removeKeyboardListeners()
    },
    
    // ==================== 键盘事件处理 ====================
    
    addKeyboardListeners() {
      if (typeof window !== 'undefined') {
        window.addEventListener('keydown', this.handleKeyDown, true)
        window.addEventListener('keyup', this.handleKeyUp, true)
      }
    },
    
    removeKeyboardListeners() {
      if (typeof window !== 'undefined') {
        window.removeEventListener('keydown', this.handleKeyDown, true)
        window.removeEventListener('keyup', this.handleKeyUp, true)
      }
    },
    
    handleKeyDown(event) {
      if (!this.isListening) return

      // 支持ESC键取消监听
      if (event.key === 'Escape') {
        console.log('ESC pressed, canceling hotkey listening')
        this.stopListening()
        return
      }

      event.preventDefault()
      event.stopPropagation()

      const key = this.normalizeKey(event.key, event.code)

      if (!this.pressedKeys.has(key)) {
        this.pressedKeys.add(key)
        this.keySequence.push(key)

        console.log('按键按下:', key, '当前组合:', this.getCurrentKeyCombo)

        // 实时更新显示（触发getter重新计算）
        this.keySequence = [...this.keySequence]
      }
    },
    
    handleKeyUp(event) {
      if (!this.isListening) return
      
      event.preventDefault()
      event.stopPropagation()
      
      const key = this.normalizeKey(event.key, event.code)
      this.pressedKeys.delete(key)
      
      // 如果所有按键都释放了，完成快捷键设置
      if (this.pressedKeys.size === 0 && this.keySequence.length > 0) {
        this.completeHotkeyBinding()
      }
    },
    
    // ==================== 按键标准化 ====================
    
    normalizeKey(key, code) {
      // 标准化修饰键名称
      const keyMap = {
        'Control': 'Ctrl',
        'ControlLeft': 'Ctrl',
        'ControlRight': 'Ctrl',
        'ShiftLeft': 'Shift',
        'ShiftRight': 'Shift',
        'AltLeft': 'Alt',
        'AltRight': 'Alt',
        'MetaLeft': 'Meta',
        'MetaRight': 'Meta',
        'CmdLeft': 'Cmd',
        'CmdRight': 'Cmd'
      }
      
      // 处理功能键
      if (code && code.startsWith('F') && /^F\d+$/.test(code)) {
        return code
      }
      
      // 处理字母和数字键
      if (key.length === 1 && /[a-zA-Z0-9]/.test(key)) {
        return key.toUpperCase()
      }
      
      return keyMap[key] || keyMap[code] || key
    },
    
    // ==================== 快捷键绑定完成 ====================
    
    completeHotkeyBinding() {
      if (!this.currentListeningKey || this.keySequence.length === 0) {
        this.stopListening()
        return
      }
      
      const keyCombo = this.getCurrentKeyCombo
      
      if (keyCombo) {
        // 更新快捷键配置
        this.hotkeys[this.currentListeningKey] = keyCombo
        
        // 添加到待绑定列表
        this.pendingBindings[this.currentListeningKey] = {
          key: keyCombo,
          timestamp: new Date().toISOString(),
          action: this.getActionForKeyType(this.currentListeningKey)
        }
        
        console.log(`快捷键设置完成: ${this.currentListeningKey} = ${keyCombo}`)
      }
      
      this.stopListening()
    },
    
    // ==================== 快捷键绑定数据管理 ====================
    
    getActionForKeyType(keyType) {
      const actionMap = {
        monitoring: 'toggle_monitoring',
        automation: 'toggle_automation',
        export: 'export_session',
        screenshot: 'take_screenshot'
      }
      return actionMap[keyType] || keyType
    },
    
    // 发送快捷键绑定到后端
    async syncHotkeysToBackend() {
      if (Object.keys(this.pendingBindings).length === 0) {
        return { success: true, message: '没有待同步的快捷键' }
      }

      try {
        // 使用新的配置API
        const { configAPI } = await import('@/api')
        const { useAuthStore } = await import('@/stores/auth')
        const authStore = useAuthStore()
        const roomName = authStore.user?.roomName || 'default'

        // 准备快捷键配置数据
        const hotkeysConfig = {}

        // 将pendingBindings转换为标准格式
        for (const [keyType, binding] of Object.entries(this.pendingBindings)) {
          hotkeysConfig[keyType] = binding.key
        }

        console.log('📤 保存快捷键配置:', hotkeysConfig)

        // 调用配置API保存快捷键配置
        const response = await configAPI.hotkeys.save(roomName, hotkeysConfig)

        if (response.data && response.data.success) {
          // 更新本地快捷键配置
          for (const [keyType, key] of Object.entries(hotkeysConfig)) {
            this.hotkeys[keyType] = key
          }

          // 清空待绑定列表
          this.pendingBindings = {}

          console.log('✅ 快捷键配置保存成功:', hotkeysConfig)
          return { success: true, message: '快捷键配置保存成功' }
        } else {
          throw new Error(response.data?.message || '保存失败')
        }

      } catch (error) {
        console.error('❌ 快捷键同步错误:', error)
        return { success: false, message: `更新快捷键配置失败: ${error.message}` }
      }
    },
    
    // 重置快捷键配置
    resetHotkey(keyType) {
      if (this.hotkeys[keyType]) {
        delete this.hotkeys[keyType]
        delete this.pendingBindings[keyType]
        console.log(`快捷键已重置: ${keyType}`)
      }
    },
    
    // 重置所有快捷键
    resetAllHotkeys() {
      this.hotkeys = {
        monitoring: 'F1',
        automation: 'F2',
        export: 'F3',
        screenshot: 'F4'
      }
      this.pendingBindings = {}
      console.log('所有快捷键已重置为默认值')
    },
    
    // 从后端加载快捷键配置
    async loadHotkeysFromBackend() {
      try {
        console.log('📥 开始加载快捷键配置...')

        // 首先尝试从localStorage加载
        const cachedHotkeys = localStorage.getItem('hotkeys_config')
        if (cachedHotkeys) {
          try {
            const hotkeysConfig = JSON.parse(cachedHotkeys)
            this.hotkeys = { ...this.hotkeys, ...hotkeysConfig }
            console.log('✅ 从localStorage加载快捷键配置成功:', this.hotkeys)
            return { success: true, message: '快捷键配置加载成功' }
          } catch (parseError) {
            console.warn('⚠️ 解析localStorage中的快捷键配置失败:', parseError)
          }
        }

        // 如果localStorage中没有，则从Server端API加载
        try {
          const { configAPI } = await import('@/api')
          const { useAuthStore } = await import('@/stores/auth')
          const authStore = useAuthStore()
          const roomName = authStore.user?.roomName || 'default'

          const response = await configAPI.hotkeys.get(roomName)

          if (response.data && response.data.success) {
            const hotkeysConfig = response.data.data
            this.hotkeys = { ...this.hotkeys, ...hotkeysConfig }

            // 更新localStorage缓存
            localStorage.setItem('hotkeys_config', JSON.stringify(hotkeysConfig))

            console.log('✅ 从Server端API加载快捷键配置成功:', this.hotkeys)
            return { success: true, message: '快捷键配置加载成功' }
          } else {
            console.warn('⚠️ Server端API返回失败，使用默认配置')
          }
        } catch (apiError) {
          console.error('❌ 从Server端API加载快捷键配置失败:', apiError)
        }

        console.log('使用默认快捷键配置')
        return { success: true, message: '使用默认配置' }
      } catch (error) {
        console.error('❌ 加载快捷键配置失败:', error)
        return { success: false, message: `加载失败: ${error.message}` }
      }
    }
  }
})
