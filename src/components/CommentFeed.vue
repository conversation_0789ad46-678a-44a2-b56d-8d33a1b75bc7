<template>
  <div class="comment-feed">
    <div class="feed-header">
      <div class="feed-title">
        <el-icon><ChatDotRound /></el-icon>
        <span>弹幕评论流</span>
      </div>
      
      <div class="feed-status">
        <el-tag
          :type="websocketStore.isConnected ? 'success' : 'danger'"
          size="small"
        >
          <el-icon><Connection v-if="websocketStore.isConnected" /><CircleClose v-else /></el-icon>
          {{ websocketStore.isConnected ? '已连接' : '未连接' }}
        </el-tag>
      </div>
    </div>
    
    <div
      class="feed-content"
      ref="feedContentRef"
      @scroll="handleScroll"
    >
      <div v-if="websocketStore.comments.length === 0" class="empty-feed">
        <el-empty description="暂无评论" />
      </div>

      <div v-else class="comment-list">
        <div
          v-for="comment in websocketStore.comments"
          :key="comment.id"
          class="comment-item"
          :class="{
            'selected': websocketStore.selectedCommentId === comment.id,
            'processing': comment.status === 'processing',
            'completed': comment.status === 'completed',
            'failed': comment.status === 'failed',
            'analyzing': comment.status === 'analyzing'
          }"
          @click="selectComment(comment)"
        >
          <div class="comment-header">
            <!-- 移除用户信息显示，保持界面简洁 -->
            <div class="comment-time">
              {{ formatTime(comment.timestamp) }}
            </div>
            <div class="comment-status">
              <el-tag
                :type="getStatusType(comment.status)"
                size="small"
              >
                {{ formatField('status', comment.status) }}
              </el-tag>
            </div>
          </div>

          <div class="comment-content">
            {{ truncateText(comment.content, 100) }}
          </div>

          <!-- 移除intent和method_type显示，保持列表简洁 -->
          <!-- 详细信息在右侧面板中查看 -->
        </div>
      </div>
    </div>

    <!-- 滚动到底部悬浮按钮 -->
    <transition name="fade">
      <div
        v-if="websocketStore.showScrollToBottom"
        class="scroll-to-bottom-btn"
        @click="scrollToBottom"
      >
        <el-button
          type="primary"
          :icon="ArrowDown"
          size="small"
          round
        >
          滚动到最新处
        </el-button>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useWebSocketStore } from '@/stores/websocket'
import { ChatDotRound, Connection, CircleClose, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  autoScroll: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['comment-selected'])

const websocketStore = useWebSocketStore()
const feedContentRef = ref(null)
const scrollTimeout = ref(null)

const selectComment = (comment) => {
  console.log('🎯 用户点击选择弹幕:', comment.id)
  // 使用store中的方法来选择评论，传入完整的comment对象
  websocketStore.selectComment(comment)
  emit('comment-selected', comment)
  console.log('✅ 弹幕选择事件已触发')
}

// 滚动到底部
const scrollToBottom = () => {
  if (feedContentRef.value) {
    feedContentRef.value.scrollTop = feedContentRef.value.scrollHeight
    websocketStore.scrollToBottom()
  }
}

// 检查是否在底部
const isAtBottom = () => {
  if (!feedContentRef.value) return true

  const { scrollTop, scrollHeight, clientHeight } = feedContentRef.value
  const threshold = 50 // 50px的容差
  return scrollHeight - scrollTop - clientHeight <= threshold
}

// 处理滚动事件
const handleScroll = () => {
  // 防抖处理
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  scrollTimeout.value = setTimeout(() => {
    const atBottom = isAtBottom()
    websocketStore.checkScrollPosition(atBottom)
  }, 100)
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' })
}

const formatStatus = (status) => {
  if (!status) return '未知'

  const statusMap = {
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'analyzing': '意图分析中'
  }

  return statusMap[status] || status
}

const formatMethod = (method) => {
  if (!method) return ''

  const methodMap = {
    'voice_reply': '语音回复',
    'automation': '自动化',
    'ignore': '忽略'
  }

  return methodMap[method] || method
}

// 新的DanmuMessage字段格式化函数
const formatField = (field, value) => {
  // 使用WebSocket store中的格式化工具
  return websocketStore.formatDanmuField(field, value)
}

const getStatusType = (status) => {
  const typeMap = {
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'analyzing': 'info'
  }
  
  return typeMap[status] || 'info'
}

// 监听评论数量变化，实现智能滚动
watch(() => websocketStore.comments.length, async () => {
  if (websocketStore.autoScroll && feedContentRef.value) {
    await nextTick()
    scrollToBottom()
  }
})

// 组件挂载时初始化
onMounted(() => {
  // 初始化时滚动到底部
  nextTick(() => {
    if (feedContentRef.value && websocketStore.comments.length > 0) {
      scrollToBottom()
    }
  })
})

// 组件卸载时清理
onUnmounted(() => {
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }
  // 取消选中状态
  websocketStore.unselectComment()
})
</script>

<style scoped>
.comment-feed {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(45, 45, 68, 0.95);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.feed-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
}

.feed-status {
  display: flex;
  align-items: center;
}

.feed-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.empty-feed {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comment-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.comment-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.comment-item.selected {
  background: rgba(88, 101, 242, 0.15);
  border-left-color: #5865f2;
}

.comment-item.processing {
  border-left-color: #e6a23c;
}

.comment-item.completed {
  border-left-color: #67c23a;
}

.comment-item.failed {
  border-left-color: #f56c6c;
}

.comment-item.analyzing {
  border-left-color: #909399;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

/* 用户信息样式已移除，保持界面简洁 */

.comment-time {
  font-size: 12px;
  color: #a0a0a0;
  white-space: nowrap;
}

.comment-content {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
}

/* comment-footer样式已移除，因为不再显示intent和method_type */

:deep(.el-tag) {
  border-radius: 4px;
}

:deep(.el-empty__description) {
  color: #a0a0a0;
}

/* 滚动到底部悬浮按钮 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border-radius: 20px;
}

.scroll-to-bottom-btn .el-button {
  background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
  border: none;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  box-shadow: 0 2px 8px rgba(88, 101, 242, 0.3);
}

.scroll-to-bottom-btn .el-button:hover {
  background: linear-gradient(135deg, #4752c4 0%, #3c47a0 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(88, 101, 242, 0.4);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
