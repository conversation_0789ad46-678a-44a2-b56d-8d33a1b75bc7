import { defineStore } from 'pinia'
import { processAPI } from '@/api'

export const useProcessStore = defineStore('process', {
  state: () => ({
    streamingProcess: '',
    targetProcess: '',
    isMonitoring: false,
    isAutomating: false,
    loading: {
      monitoring: false,
      automation: false
    },
    error: null
  }),

  getters: {
    canStartMonitoring: (state) => !!state.streamingProcess && !state.isMonitoring,
    canStartAutomation: (state) => !!state.targetProcess && !state.isAutomating,
    isConfigured: (state) => !!state.streamingProcess && !!state.targetProcess
  },

  actions: {
    setStreamingProcess(processName) {
      this.streamingProcess = processName
    },

    setTargetProcess(processName) {
      this.targetProcess = processName
    },

    async startProcessMonitoring() {
      if (!this.streamingProcess) {
        this.error = '直播软件进程名称为必填项'
        return { success: false, error: this.error }
      }

      this.loading.monitoring = true
      this.error = null

      try {
        const response = await processAPI.startMonitoring(this.streamingProcess)
        this.isMonitoring = true
        return { success: true, data: response.data }
      } catch (error) {
        this.error = error.response?.data?.message || '启动监控失败'
        return { success: false, error: this.error }
      } finally {
        this.loading.monitoring = false
      }
    },

    async stopProcessMonitoring() {
      this.loading.monitoring = true

      try {
        const response = await processAPI.stopMonitoring()
        this.isMonitoring = false
        return { success: true, data: response.data }
      } catch (error) {
        this.error = error.response?.data?.message || '停止监控失败'
        return { success: false, error: this.error }
      } finally {
        this.loading.monitoring = false
      }
    },

    async startAutomation() {
      if (!this.targetProcess) {
        this.error = '目标进程名称为必填项'
        return { success: false, error: this.error }
      }

      this.loading.automation = true
      this.error = null

      try {
        const response = await processAPI.startAutomation(this.targetProcess)
        this.isAutomating = true
        return { success: true, data: response.data }
      } catch (error) {
        this.error = error.response?.data?.message || '启动自动化失败'
        return { success: false, error: this.error }
      } finally {
        this.loading.automation = false
      }
    },

    async stopAutomation() {
      this.loading.automation = true

      try {
        console.log('🛑 Stopping automation...')
        console.log('📡 Calling backend API: stop_automation()')
        const response = await processAPI.stopAutomation()
        console.log('📡 Backend response:', response)

        // 检查响应格式
        const result = response.data || response

        if (result.success) {
          // 立即更新前端状态
          this.isAutomating = false
          this.error = null

          console.log('✅ Automation stopped successfully:', result.message)
          return { success: true, data: result }
        } else {
          this.error = result.message || '停止自动化失败'
          console.error('❌ Failed to stop automation:', this.error)
          return { success: false, error: this.error }
        }
      } catch (error) {
        console.error('💥 Error stopping automation:', error)
        this.error = error.response?.data?.message || error.message || '停止自动化失败'
        return { success: false, error: this.error }
      } finally {
        this.loading.automation = false
      }
    },

    toggleMonitoring() {
      return this.isMonitoring ? this.stopProcessMonitoring() : this.startProcessMonitoring()
    },

    toggleAutomation() {
      return this.isAutomating ? this.stopAutomation() : this.startAutomation()
    },

    // 清理所有数据，用于退出登录时重置状态
    clearAllData() {
      console.log('🧹 清理Process store所有数据...')

      // 重置进程配置
      this.streamingProcess = ''
      this.targetProcess = ''

      // 重置状态
      this.isMonitoring = false
      this.isAutomating = false

      // 重置加载状态
      this.loading.monitoring = false
      this.loading.automation = false

      // 清除错误信息
      this.error = null

      console.log('✅ Process store数据已清理')
    }
  }
})
