import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import './styles/global.css'

// Debug: Check environment before creating app
console.log('=== Vue App Starting ===')
console.log('DOM Ready:', document.readyState)
console.log('App element exists:', !!document.getElementById('app'))

const app = createApp(App)

// Add global error handler first
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', vm)
  console.error('Info:', info)
}

// Register all Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// Debug: Check pywebview environment after plugins
console.log('=== Environment Check ===')
console.log('window.pywebview:', typeof window !== 'undefined' ? window.pywebview : 'undefined')
console.log('window.pywebview.api:', typeof window !== 'undefined' && window.pywebview ? window.pywebview.api : 'undefined')

// Mount the app
try {
  app.mount('#app')
  console.log('✓ Vue app mounted successfully')
} catch (error) {
  console.error('❌ Failed to mount Vue app:', error)
}
