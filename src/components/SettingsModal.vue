<template>
  <el-dialog
    v-model="visible"
    title="系统设置"
    width="600px"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 快捷键设置 -->
      <el-tab-pane label="快捷键设置" name="hotkeys">
        <div class="hotkeys-section">
          <div class="section-header">
            <h3>快捷键配置</h3>
            <p v-if="!hotkeysStore.isListening">点击输入框后按下您想要设置的快捷键组合</p>
            <p v-else class="listening-tip">
              <el-icon class="listening-icon-inline"><Loading /></el-icon>
              正在监听按键... (按 ESC 取消)
            </p>
          </div>
          
          <div class="hotkey-list">
            <div 
              v-for="(label, key) in hotkeyLabels" 
              :key="key"
              class="hotkey-item"
            >
              <label class="hotkey-label">{{ label }}：</label>
              <div class="hotkey-input-group">
                <el-input
                  :value="getDisplayValue(key)"
                  :placeholder="`点击设置${label}快捷键`"
                  readonly
                  :class="{
                    'listening': hotkeysStore.currentListeningKey === key,
                    'has-value': hotkeysStore.getFormattedHotkey(key) !== '未设置'
                  }"
                  @focus="startListening(key)"
                >
                  <template #suffix>
                    <el-icon
                      v-if="hotkeysStore.currentListeningKey === key"
                      class="listening-icon"
                    >
                      <Loading />
                    </el-icon>
                  </template>
                </el-input>
                <el-button
                  size="small"
                  type="danger"
                  :icon="Delete"
                  @click="resetHotkey(key)"
                  title="重置快捷键"
                />
              </div>
            </div>
          </div>
          
          <div class="hotkey-actions">
            <el-button
              type="primary"
              :loading="syncLoading"
              :disabled="!hotkeysStore.hasPendingBindings"
              @click="syncHotkeys"
            >
              保存快捷键设置
            </el-button>
            <el-button
              type="warning"
              @click="resetAllHotkeys"
            >
              重置所有快捷键
            </el-button>
          </div>
          
          <div v-if="hotkeysStore.hasPendingBindings" class="pending-changes">
            <el-alert
              title="有未保存的快捷键更改"
              type="warning"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="pending-list">
                  <div
                    v-for="(binding, key) in hotkeysStore.pendingBindings"
                    :key="key"
                    class="pending-item"
                  >
                    <strong>{{ hotkeyLabels[key] }}</strong>: {{ binding.key }}
                  </div>
                </div>
              </template>
            </el-alert>
          </div>


        </div>
      </el-tab-pane>
      
      <!-- 路径配置 -->
      <el-tab-pane label="路径配置" name="paths">
        <div class="paths-section">
          <div class="section-header">
            <h3>缓存路径配置</h3>
            <p>配置各种文件的存储位置</p>
          </div>

          <div class="path-list">
            <div class="path-item">
              <label class="path-label">截图缓存位置：</label>
              <div class="path-input-group">
                <el-input
                  v-model="pathSettings.screenshot"
                  placeholder="选择截图保存目录"
                  readonly
                >
                  <template #suffix>
                    <el-icon class="folder-icon">
                      <Folder />
                    </el-icon>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  :icon="FolderOpened"
                  @click="selectPath('screenshot')"
                >
                  选择目录
                </el-button>
              </div>
              <p class="path-description">用于存储直播截图和自动截图文件</p>
            </div>

            <div class="path-item">
              <label class="path-label">音频缓存位置：</label>
              <div class="path-input-group">
                <el-input
                  v-model="pathSettings.audio"
                  placeholder="选择音频保存目录"
                  readonly
                >
                  <template #suffix>
                    <el-icon class="folder-icon">
                      <Folder />
                    </el-icon>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  :icon="FolderOpened"
                  @click="selectPath('audio')"
                >
                  选择目录
                </el-button>
              </div>
              <p class="path-description">用于存储语音回复和音频处理文件</p>
            </div>

            <div class="path-item">
              <label class="path-label">模型缓存位置：</label>
              <div class="path-input-group">
                <el-input
                  v-model="pathSettings.model"
                  placeholder="选择模型保存目录"
                  readonly
                >
                  <template #suffix>
                    <el-icon class="folder-icon">
                      <Folder />
                    </el-icon>
                  </template>
                </el-input>
                <el-button
                  type="primary"
                  :icon="FolderOpened"
                  @click="selectPath('model')"
                >
                  选择目录
                </el-button>
              </div>
              <p class="path-description">用于存储AI模型文件和训练数据</p>
            </div>
          </div>

          <div class="path-actions">
            <el-button
              type="primary"
              :loading="pathSyncLoading"
              @click="syncPathSettings"
            >
              保存路径配置
            </el-button>
            <el-button
              type="warning"
              @click="resetPathSettings"
            >
              重置为默认路径
            </el-button>
          </div>
        </div>
      </el-tab-pane>


    </el-tabs>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useHotkeysStore } from '@/stores/hotkeys'
import { Loading, Delete, Folder, FolderOpened } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const hotkeysStore = useHotkeysStore()

const visible = ref(false)
const activeTab = ref('hotkeys')
const syncLoading = ref(false)
const pathSyncLoading = ref(false)

// 路径设置
const pathSettings = ref({
  screenshot: '',
  audio: '',
  model: ''
})

// 快捷键标签映射
const hotkeyLabels = {
  monitoring: '弹幕监控',
  automation: '自动化控制'
}

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取显示值（实时显示正在输入的快捷键）
const getDisplayValue = (keyType) => {
  if (hotkeysStore.currentListeningKey === keyType && hotkeysStore.isListening) {
    // 正在监听时显示当前按键组合
    const currentCombo = hotkeysStore.getCurrentKeyCombo
    return currentCombo || '请按下快捷键...'
  }
  // 否则显示已保存的快捷键
  return hotkeysStore.getFormattedHotkey(keyType)
}

// 开始监听快捷键
const startListening = (keyType) => {
  if (hotkeysStore.isListening) {
    hotkeysStore.stopListening()
  }
  console.log(`开始监听快捷键: ${hotkeyLabels[keyType]}`)
  hotkeysStore.startListening(keyType)
}

// 重置单个快捷键
const resetHotkey = (keyType) => {
  hotkeysStore.resetHotkey(keyType)
  ElMessage.success(`${hotkeyLabels[keyType]}快捷键已重置`)
}

// 重置所有快捷键
const resetAllHotkeys = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有快捷键为默认值吗？',
      '确认重置',
      {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    hotkeysStore.resetAllHotkeys()
    ElMessage.success('所有快捷键已重置为默认值')
  } catch {
    // 用户取消
  }
}

// 同步快捷键到后端
const syncHotkeys = async () => {
  syncLoading.value = true
  try {
    const result = await hotkeysStore.syncHotkeysToBackend()
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error(`同步失败: ${error.message}`)
  } finally {
    syncLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  // 停止监听快捷键
  if (hotkeysStore.isListening) {
    hotkeysStore.stopListening()
  }
  visible.value = false
}

// 路径选择
const selectPath = async (pathType) => {
  try {
    // 检查是否在pywebview环境中
    if (typeof window !== 'undefined' && window.pywebview && window.pywebview.api) {
      const result = await window.pywebview.api.select_directory(pathType)
      if (result.success && result.path) {
        pathSettings.value[pathType] = result.path
        ElMessage.success(`${getPathLabel(pathType)}路径已选择`)
      } else {
        ElMessage.warning('未选择路径')
      }
    } else {
      // 开发环境下模拟路径选择
      const mockPaths = {
        screenshot: '/Users/<USER>/Documents/智能主播助手/截图',
        audio: '/Users/<USER>/Documents/智能主播助手/音频',
        model: '/Users/<USER>/Documents/智能主播助手/模型'
      }
      pathSettings.value[pathType] = mockPaths[pathType]
      ElMessage.success(`${getPathLabel(pathType)}路径已选择（模拟）`)
    }
  } catch (error) {
    ElMessage.error(`选择路径失败: ${error.message}`)
  }
}

// 获取路径类型标签
const getPathLabel = (pathType) => {
  const labels = {
    screenshot: '截图缓存',
    audio: '音频缓存',
    model: '模型缓存'
  }
  return labels[pathType] || pathType
}

// 同步路径设置到后端
const syncPathSettings = async () => {
  pathSyncLoading.value = true
  try {
    // 转换前端字段到后端格式
    const pathsConfig = {
      screenshot: pathSettings.value.screenshot,
      voice_cache: pathSettings.value.audio,  // 前端audio字段对应后端voice_cache
      models: pathSettings.value.model
    }

    console.log('📁 保存路径配置:', pathsConfig)

    // 调用配置API保存路径配置
    const { configAPI } = await import('@/api')
    const { useAuthStore } = await import('@/stores/auth')
    const authStore = useAuthStore()
    const roomName = authStore.user?.roomName || 'default'

    const response = await configAPI.paths.save(roomName, pathsConfig)

    if (response.data && response.data.success) {
      // 更新localStorage
      localStorage.setItem('cache_paths', JSON.stringify(pathsConfig))

      console.log('✅ 路径配置保存成功:', pathsConfig)
      ElMessage.success('路径配置保存成功')
    } else {
      throw new Error(response.data?.message || '保存失败')
    }

  } catch (error) {
    console.error('❌ 保存路径配置失败:', error)
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`)
  } finally {
    pathSyncLoading.value = false
  }
}

// 重置路径设置
const resetPathSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有路径为默认值吗？这将使用程序当前运行目录作为基础路径。',
      '确认重置',
      {
        confirmButtonText: '重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    pathSyncLoading.value = true

    try {
      // 获取程序绝对路径
      let basePath = ''

      // 检查是否在pywebview环境中
      if (typeof window !== 'undefined' && window.pywebview && window.pywebview.api) {
        try {
          // 使用pywebview API获取项目路径
          const pathResult = await window.pywebview.api.get_project_path()
          if (pathResult && pathResult.success) {
            basePath = pathResult.path
            console.log('✅ 通过pywebview API获取项目路径:', basePath)
          } else {
            throw new Error('pywebview API返回失败')
          }
        } catch (apiError) {
          console.warn('⚠️ pywebview API获取路径失败，使用默认路径:', apiError)
          basePath = '/app'  // 默认路径
        }
      } else {
        // 开发环境下使用模拟路径
        basePath = process.cwd ? process.cwd() : '/Users/<USER>/Documents/智能主播助手'
        console.log('🔧 开发环境使用模拟项目路径:', basePath)
      }

      // 生成默认路径配置
      const defaultPaths = {
        screenshot: `${basePath}/screenshot`,
        voice_cache: `${basePath}/voice_cache`,
        models: `${basePath}/models`
      }

      console.log('📁 生成的默认路径配置:', defaultPaths)

      // 调用Server端接口保存新的路径配置
      const { configAPI } = await import('@/api')
      const { useAuthStore } = await import('@/stores/auth')
      const authStore = useAuthStore()
      const roomName = authStore.user?.roomName || 'default'

      const response = await configAPI.paths.reset(roomName, basePath)

      if (response.data && response.data.success) {
        // 更新前端UI显示
        pathSettings.value = {
          screenshot: defaultPaths.screenshot,
          audio: defaultPaths.voice_cache,  // 映射到audio字段
          model: defaultPaths.models
        }

        // 更新localStorage
        localStorage.setItem('cache_paths', JSON.stringify(defaultPaths))

        console.log('✅ 默认路径重置成功:', defaultPaths)
        ElMessage.success('路径配置已重置为默认值')
      } else {
        throw new Error(response.data?.message || '重置失败')
      }

    } catch (error) {
      console.error('❌ 重置默认路径失败:', error)
      ElMessage.error(`重置失败: ${error.message || '未知错误'}`)
    } finally {
      pathSyncLoading.value = false
    }

  } catch {
    // 用户取消
  }
}

// 加载路径配置
const loadPathSettings = async () => {
  try {
    console.log('📁 加载路径配置...')

    // 首先尝试从localStorage加载
    const cachedPaths = localStorage.getItem('cache_paths')
    if (cachedPaths) {
      try {
        const pathsConfig = JSON.parse(cachedPaths)
        // 转换后端格式到前端字段
        pathSettings.value = {
          screenshot: pathsConfig.screenshot || '',
          audio: pathsConfig.voice_cache || '',  // 后端voice_cache对应前端audio
          model: pathsConfig.models || ''
        }
        console.log('✅ 从localStorage加载路径配置成功:', pathSettings.value)
        return
      } catch (parseError) {
        console.warn('⚠️ 解析localStorage中的路径配置失败:', parseError)
      }
    }

    // 如果localStorage中没有，则从Server端API加载
    try {
      const { configAPI } = await import('@/api')
      const { useAuthStore } = await import('@/stores/auth')
      const authStore = useAuthStore()
      const roomName = authStore.user?.roomName || 'default'

      const response = await configAPI.paths.get(roomName)

      if (response.data && response.data.success) {
        const pathsConfig = response.data.data
        // 转换后端格式到前端字段
        pathSettings.value = {
          screenshot: pathsConfig.screenshot || '',
          audio: pathsConfig.voice_cache || '',  // 后端voice_cache对应前端audio
          model: pathsConfig.models || ''
        }

        // 更新localStorage缓存
        localStorage.setItem('cache_paths', JSON.stringify(pathsConfig))

        console.log('✅ 从Server端API加载路径配置成功:', pathSettings.value)
      } else {
        console.warn('⚠️ Server端API返回失败，使用默认配置')
      }
    } catch (apiError) {
      console.error('❌ 从Server端API加载路径配置失败:', apiError)
    }

  } catch (error) {
    console.error('❌ 加载路径配置失败:', error)
  }
}

// 组件挂载时加载配置
onMounted(() => {
  hotkeysStore.loadHotkeysFromBackend()
  loadPathSettings()
})

// 组件卸载时清理
onUnmounted(() => {
  if (hotkeysStore.isListening) {
    hotkeysStore.stopListening()
  }
})
</script>

<style scoped>
.hotkeys-section {
  padding: 20px 0;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 18px;
}

.section-header p {
  margin: 0;
  color: #a0a0a0;
  font-size: 14px;
}

.hotkey-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.hotkey-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hotkey-label {
  min-width: 100px;
  color: #ffffff;
  font-weight: 500;
}

.hotkey-input-group {
  display: flex;
  gap: 8px;
  flex: 1;
}

.hotkey-input-group .el-input {
  flex: 1;
}

.listening {
  border-color: #5865f2 !important;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2) !important;
  background: rgba(88, 101, 242, 0.05) !important;
}

.has-value {
  border-color: #48bb78 !important;
}

.listening-icon {
  animation: spin 1s linear infinite;
  color: #5865f2;
}

.listening-icon-inline {
  animation: spin 1s linear infinite;
  color: #5865f2;
  margin-right: 8px;
}

.listening-tip {
  color: #5865f2 !important;
  font-weight: 500;
  display: flex;
  align-items: center;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hotkey-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.pending-changes {
  margin-top: 16px;
}

.pending-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pending-item {
  color: #e6a23c;
  font-size: 14px;
}

.paths-section {
  padding: 20px 0;
}

.path-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 32px;
}

.path-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.path-label {
  color: #ffffff;
  font-weight: 500;
  font-size: 14px;
}

.path-input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.path-input-group .el-input {
  flex: 1;
}

.path-description {
  color: #a0a0a0;
  font-size: 12px;
  margin: 0;
  margin-left: 4px;
}

.folder-icon {
  color: #a0a0a0;
}

.path-actions {
  display: flex;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #3d3d5c;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 深色主题样式 */
:deep(.el-dialog) {
  background: #2d2d44;
  border: 1px solid #3d3d5c;
  color: #ffffff;
}

:deep(.el-dialog__header) {
  background: #1e1e2e;
  border-bottom: 1px solid #3d3d5c;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #a0a0a0;
}

:deep(.el-dialog__headerbtn .el-dialog__close:hover) {
  color: #ffffff;
}

:deep(.el-dialog__body) {
  background: #2d2d44;
  padding: 0;
}

:deep(.el-dialog__footer) {
  background: #2d2d44;
  border-top: 1px solid #3d3d5c;
  padding: 16px 24px;
}

:deep(.el-tabs) {
  background: transparent;
}

:deep(.el-tabs__header) {
  background: #1e1e2e;
  margin: 0;
  border-bottom: 1px solid #3d3d5c;
}

:deep(.el-tabs__nav-wrap) {
  background: #1e1e2e;
}

:deep(.el-tabs__nav) {
  background: #1e1e2e;
}

:deep(.el-tabs__item) {
  color: #a0a0a0;
  background: #1e1e2e;
  border-right: 1px solid #3d3d5c;
  padding: 0 20px;
  height: 48px;
  line-height: 48px;
}

:deep(.el-tabs__item:hover) {
  color: #ffffff;
}

:deep(.el-tabs__item.is-active) {
  color: #5865f2;
  background: #2d2d44;
  border-bottom-color: #2d2d44;
}

:deep(.el-tabs__content) {
  background: #2d2d44;
  min-height: 400px;
}

:deep(.el-tab-pane) {
  background: #2d2d44;
  color: #ffffff;
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  background: #1e1e2e;
  border: 1px solid #3d3d5c;
  box-shadow: none;
}

:deep(.el-input__wrapper:hover) {
  border-color: #5865f2;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #5865f2;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
}

:deep(.el-input__inner) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #6b6b7d;
}

/* 按钮样式 */
:deep(.el-button) {
  border: 1px solid #3d3d5c;
  background: #1e1e2e;
  color: #ffffff;
}

:deep(.el-button:hover) {
  background: #3d3d5c;
  border-color: #5865f2;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
  border-color: #5865f2;
  color: #ffffff;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #4752c4 0%, #3c47a0 100%);
}

:deep(.el-button--danger) {
  background: #f56565;
  border-color: #f56565;
}

:deep(.el-button--danger:hover) {
  background: #e53e3e;
}

:deep(.el-button--warning) {
  background: #ed8936;
  border-color: #ed8936;
}

:deep(.el-button--warning:hover) {
  background: #dd6b20;
}

/* 开关样式 */
:deep(.el-switch__core) {
  background: #3d3d5c;
  border-color: #3d3d5c;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background: #5865f2;
  border-color: #5865f2;
}

/* 标签样式 */
:deep(.el-tag) {
  background: #1e1e2e;
  border-color: #3d3d5c;
  color: #ffffff;
}

:deep(.el-tag--success) {
  background: rgba(72, 187, 120, 0.2);
  border-color: #48bb78;
  color: #48bb78;
}

:deep(.el-tag--danger) {
  background: rgba(245, 101, 101, 0.2);
  border-color: #f56565;
  color: #f56565;
}

/* 警告框样式 */
:deep(.el-alert) {
  background: rgba(237, 137, 54, 0.1);
  border: 1px solid rgba(237, 137, 54, 0.3);
}

:deep(.el-alert__title) {
  color: #ed8936;
}

:deep(.el-alert__content) {
  color: #ffffff;
}
</style>
