<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('✓ App.vue mounted successfully')

  // Add cleanup handler
  window.addEventListener('beforeunload', () => {
    console.log('App unloading...')
  })
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
  color: #ffffff;
}

body {
  margin: 0;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d44;
}

::-webkit-scrollbar-thumb {
  background: #5865f2;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4752c4;
}
</style>
