<template>
  <div class="comment-details">
    <div class="details-header">
      <div class="details-title">
        <el-icon><InfoFilled /></el-icon>
        <span>评论详情</span>
      </div>
    </div>
    
    <div class="details-content" ref="detailsContentRef">
      <div v-if="!props.selectedComment" class="no-selection">
        <el-empty description="选择评论查看详情" />
      </div>

      <div v-else class="comment-info">
        <!-- Original Comment -->
        <div class="info-section">
          <h4>原始评论</h4>
          <div class="comment-text">
            {{ props.selectedComment.content }}
          </div>
        </div>

        <!-- Processing Status -->
        <div class="info-section">
          <h4>处理状态</h4>
          <div class="status-info">
            <el-tag
              :type="getStatusType(props.selectedComment.status)"
              size="large"
            >
              <el-icon>
                <Loading v-if="props.selectedComment.status === 'processing'" />
                <CircleCheck v-else-if="props.selectedComment.status === 'completed'" />
                <CircleClose v-else-if="props.selectedComment.status === 'failed'" />
                <Search v-else-if="props.selectedComment.status === 'analyzing'" />
                <QuestionFilled v-else />
              </el-icon>
              {{ formatField('status', props.selectedComment.status) }}
            </el-tag>

            <div v-if="props.selectedComment.processing_time > 0" class="processing-time">
              处理时间：{{ props.selectedComment.processing_time.toFixed(2) }}秒
            </div>
          </div>
        </div>

        <!-- User Information -->
        <div class="info-section">
          <h4>用户信息</h4>
          <div class="user-info">
            <div class="user-item">
              <span class="label">显示名称：</span>
              <span class="value">{{ formatField('user', props.selectedComment.user) }}</span>
            </div>
            <div class="user-item" v-if="props.selectedComment.user_game">
              <span class="label">游戏名称：</span>
              <span class="value">{{ formatField('user_game', props.selectedComment.user_game) }}</span>
            </div>
          </div>
        </div>

        <!-- Intent Analysis -->
        <div class="info-section">
          <h4>意图分析</h4>
          <div class="intent-info">
            <el-tag type="info" size="large">
              <el-icon><Search /></el-icon>
              {{ formatField('intent', props.selectedComment.intent) }}
            </el-tag>
          </div>
        </div>

        <!-- Processing Method -->
        <div class="info-section">
          <h4>处理方式</h4>
          <div class="method-info">
            <el-tag type="warning" size="large">
              <el-icon>
                <Microphone v-if="props.selectedComment.method_type === 'voice_reply'" />
                <Setting v-else-if="props.selectedComment.method_type === 'deal'" />
                <Search v-else-if="props.selectedComment.method_type === 'analyzing'" />
                <QuestionFilled v-else />
              </el-icon>
              {{ formatField('method_type', props.selectedComment.method_type) }}
            </el-tag>
          </div>
        </div>

        <!-- Voice Reply Content -->
        <div class="info-section">
          <h4>回复内容</h4>
          <div class="reply-content">
            <div class="reply-text">
              {{ formatField('reply', props.selectedComment.reply) }}
            </div>
          </div>
        </div>

        <!-- Voice File Information -->
        <div class="info-section" v-if="props.selectedComment.method_type === 'voice_reply'">
          <h4>语音文件</h4>
          <div class="voice-file-info">
            <div class="voice-file-item">
              <span class="label">文件名：</span>
              <span class="value">{{ formatField('voice_file_name', props.selectedComment.voice_file_name) }}</span>
            </div>
            <div class="voice-file-item">
              <span class="label">状态：</span>
              <el-tag
                :type="getVoiceStatusType(props.selectedComment.voice_file_status)"
                size="small"
              >
                <el-icon>
                  <Loading v-if="props.selectedComment.voice_file_status === 'generating'" />
                  <CircleCheck v-else-if="props.selectedComment.voice_file_status === 'completed'" />
                  <CircleClose v-else-if="props.selectedComment.voice_file_status === 'failed'" />
                  <QuestionFilled v-else />
                </el-icon>
                {{ formatField('voice_file_status', props.selectedComment.voice_file_status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- Timestamp -->
        <div class="info-section">
          <h4>时间戳</h4>
          <div class="timestamp-info">
            {{ formatFullTime(props.selectedComment.timestamp) }}
          </div>
        </div>

        <!-- Actions -->
        <div class="info-section" v-if="props.selectedComment.status === 'failed'">
          <h4>操作</h4>
          <div class="action-buttons">
            <el-button
              type="primary"
              :icon="Refresh"
              :loading="retryLoading"
              @click="handleRetry"
            >
              重试处理
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { commentAPI } from '@/api'
import { useWebSocketStore } from '@/stores/websocket'
import { ElMessage } from 'element-plus'
import {
  InfoFilled, Loading, CircleCheck, CircleClose, Search, QuestionFilled,
  Microphone, Setting, Hide, Refresh
} from '@element-plus/icons-vue'

const props = defineProps({
  selectedComment: {
    type: Object,
    default: null
  }
})

const retryLoading = ref(false)
const detailsContentRef = ref(null)

// 监听选中弹幕变化，自动滚动到顶部
watch(() => props.selectedComment, async (newComment) => {
  if (newComment && detailsContentRef.value) {
    await nextTick()
    // 平滑滚动到顶部
    detailsContentRef.value.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
    console.log('📜 详细信息面板已滚动到顶部')
  }
}, { immediate: false })

const formatStatus = (status) => {
  if (!status) return '未知'

  const statusMap = {
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'analyzing': '意图分析中'
  }

  return statusMap[status] || status
}

const formatMethod = (method) => {
  if (!method) return ''

  const methodMap = {
    'voice_reply': '语音回复',
    'automation': '自动化',
    'ignore': '忽略'
  }

  return methodMap[method] || method
}

// 新的DanmuMessage字段格式化函数
const formatField = (field, value) => {
  // 使用WebSocket store中的格式化工具
  const websocketStore = useWebSocketStore()
  return websocketStore.formatDanmuField(field, value)
}

const getStatusType = (status) => {
  const typeMap = {
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'analyzing': 'info'
  }

  return typeMap[status] || 'info'
}

const getVoiceStatusType = (voiceStatus) => {
  const statusMap = {
    'pending': 'info',
    'generating': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[voiceStatus] || 'info'
}

const formatFullTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString()
}

const handleRetry = async () => {
  if (!props.selectedComment) return
  
  try {
    retryLoading.value = true
    const result = await commentAPI.retryProcessing(props.selectedComment.id)
    
    if (result.data.success) {
      ElMessage.success('评论处理重试已启动')
    }
  } catch (error) {
    ElMessage.error('重试评论处理失败')
  } finally {
    retryLoading.value = false
  }
}
</script>

<style scoped>
.comment-details {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(45, 45, 68, 0.95);
  border-radius: 12px;
  overflow: hidden;
}

.details-header {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.details-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
}

.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.no-selection {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
}

.info-section h4 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.comment-text {
  color: #ffffff;
  font-size: 16px;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #5865f2;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.processing-time {
  color: #a0a0a0;
  font-size: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-item .label {
  color: #a0a0a0;
  font-size: 12px;
  min-width: 80px;
}

.user-item .value {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.intent-info,
.method-info {
  display: flex;
  align-items: center;
}

.reply-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid #67c23a;
}

.reply-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
}

.timestamp-info {
  color: #c0c0c0;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-tag) {
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

:deep(.el-tag.el-tag--large) {
  padding: 8px 12px;
  font-size: 14px;
}

:deep(.el-empty__description) {
  color: #a0a0a0;
}

:deep(.el-button) {
  border-radius: 6px;
}

.voice-file-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-file-item .label {
  color: #a0a0a0;
  font-size: 12px;
  min-width: 60px;
}

.voice-file-item .value {
  color: #ffffff;
  font-size: 12px;
  font-family: monospace;
  font-weight: 400;
}
</style>
