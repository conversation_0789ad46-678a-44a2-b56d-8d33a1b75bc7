/* Global Styles for Intelligent Anchor Assistant */

/* CSS Variables for consistent theming */
:root {
  /* Primary Colors */
  --primary-color: #5865f2;
  --primary-hover: #4752c4;
  --primary-active: #3c47a3;
  
  /* Background Colors */
  --bg-primary: #1e1e2e;
  --bg-secondary: #2d2d44;
  --bg-tertiary: #3a3a5c;
  --bg-overlay: rgba(45, 45, 68, 0.95);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #c0c0c0;
  --text-muted: #a0a0a0;
  --text-disabled: #6b6b7d;
  
  /* Status Colors */
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  /* Border Colors */
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(88, 101, 242, 0.3);
  
  /* Shadow */
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  overflow: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}

/* Utility Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

.border-primary { border: 1px solid var(--border-primary); }
.border-secondary { border: 1px solid var(--border-secondary); }
.border-accent { border: 1px solid var(--border-accent); }

.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

.radius-sm { border-radius: var(--radius-sm); }
.radius-md { border-radius: var(--radius-md); }
.radius-lg { border-radius: var(--radius-lg); }
.radius-xl { border-radius: var(--radius-xl); }

/* Animation Classes */
.fade-enter-active, .fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.online {
  background: rgba(103, 194, 58, 0.2);
  color: var(--success-color);
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.status-indicator.offline {
  background: rgba(245, 108, 108, 0.2);
  color: var(--danger-color);
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.status-indicator.processing {
  background: rgba(230, 162, 60, 0.2);
  color: var(--warning-color);
  border: 1px solid rgba(230, 162, 60, 0.3);
}

/* Card Styles */
.glass-card {
  background: var(--bg-overlay);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-medium);
}

.glass-card:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-heavy);
  transition: all var(--transition-normal);
}

/* Button Enhancements */
.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-secondary);
  color: var(--text-primary);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

/* Chinese Text Specific Styles */
.chinese-text {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  letter-spacing: 0.02em;
  line-height: 1.6;
}

/* Better spacing for Chinese characters */
h1, h2, h3, h4, h5, h6 {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  letter-spacing: 0.02em;
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .monitoring-panels {
    grid-template-columns: 1fr !important;
    grid-template-rows: 1fr 1fr;
  }

  .config-row {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .header-right {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .config-actions {
    flex-direction: column;
  }
}

/* Focus States for Accessibility */
.el-button:focus,
.el-input__wrapper:focus-within,
.el-select:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: rgba(255, 255, 255, 0.3);
    --border-secondary: rgba(255, 255, 255, 0.5);
    --text-secondary: #e0e0e0;
    --text-muted: #d0d0d0;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .pulse,
  .spin {
    animation: none;
  }
}

/* Connection Status Popover Styles */
.connection-status-popover {
  z-index: 3000 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid var(--border-primary) !important;
}

.connection-status-popover .el-popover__content {
  padding: 12px !important;
}
