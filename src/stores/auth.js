import { defineStore } from 'pinia'
import { authAPI } from '@/api/index.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: false,
    user: null,
    sessionId: null,
    roomInfo: null,
    loading: false,
    error: null,
    // 新增音色相关状态
    gameList: [],
    voiceList: [],
    selectedVoice: null,
    voiceSelectionRequired: false
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && state.sessionId,
    currentUser: (state) => state.user,
    currentRoom: (state) => state.roomInfo,
    authSessionId: (state) => state.sessionId,
    // 新增音色相关getters
    hasVoiceSelection: (state) => state.selectedVoice !== null,
    isVoiceSelectionRequired: (state) => state.voiceSelectionRequired,
    availableGames: (state) => state.gameList,
    availableVoices: (state) => state.voiceList,
    currentVoice: (state) => state.selectedVoice
  },

  actions: {
    async login(loginData) {
      this.loading = true
      this.error = null

      try {
        console.log('🔐 调用客户端登录API:', loginData)

        // 使用统一的API接口（支持pywebview和mock模式）
        const response = await authAPI.login(loginData)

        console.log('📡 客户端登录响应:', response.data)

        if (response.data.success) {
          const { session_id, token, user, frontend_state, server_state, gameList, voiceList } = response.data

          console.log('🔍 解析登录响应:', { session_id, token: token?.substring(0, 8) + '...', user })

          this.sessionId = session_id || user?.session_id
          console.log('💾 设置sessionId:', this.sessionId)
          this.roomInfo = user?.room_info || {}
          this.user = {
            ...user,
            username: loginData.username,
            tenantId: loginData.tenantId,
            session_id: this.sessionId,
            login_time: response.data.timestamp || Date.now()
          }
          this.isAuthenticated = true

          // 设置音色相关数据
          this.gameList = gameList || []
          this.voiceList = voiceList || []
          this.voiceSelectionRequired = true // 登录后需要选择音色
          this.selectedVoice = null

          console.log('🎮 游戏列表:', this.gameList.length, '个游戏')
          console.log('🎵 音色列表:', this.voiceList.length, '个音色')

          // 存储到localStorage
          if (this.sessionId) {
            localStorage.setItem('session_id', this.sessionId)
          }
          if (this.roomInfo) {
            localStorage.setItem('room_info', JSON.stringify(this.roomInfo))
          }
          localStorage.setItem('user_data', JSON.stringify(this.user))
          if (token) {
            localStorage.setItem('auth_token', token)
          }

          console.log('✅ 登录成功:', {
            session_id: this.sessionId,
            room_info: this.roomInfo,
            frontend_state,
            server_state
          })

          // 登录成功后立即建立WebSocket连接
          try {
            const { useWebSocketStore } = await import('@/stores/websocket')
            const websocketStore = useWebSocketStore()

            console.log('🔌 登录成功，立即建立WebSocket连接...')
            await websocketStore.connect(this.sessionId)

            // 更新连接状态
            if (websocketStore.isConnected) {
              console.log('✅ WebSocket连接已建立')
            } else {
              console.warn('⚠️ WebSocket连接建立失败')
            }
          } catch (connectError) {
            console.error('❌ 登录后建立WebSocket连接失败:', connectError)
          }

          // 初始化路径配置
          try {
            await this.initializeCachePaths()
          } catch (pathError) {
            console.error('❌ 初始化路径配置失败:', pathError)
          }

          // 加载用户配置
          try {
            await this.loadUserConfigs()
          } catch (configError) {
            console.error('❌ 加载用户配置失败:', configError)
          }

          return {
            success: true,
            session_id: this.sessionId,
            room_info: this.roomInfo,
            frontend_state,
            server_state
          }
        } else {
          // 认证失败，抛出错误以便在组件中处理
          const errorMessage = response.data.message || '登录失败'
          const errorCode = response.data.error_code || 'AUTH_FAILED'
          const errorDetails = response.data.details || {}

          console.error('❌ 认证失败:', { errorCode, errorMessage, errorDetails })

          const error = new Error(errorMessage)
          error.code = errorCode
          error.details = errorDetails
          throw error
        }
      } catch (error) {
        console.error('❌ 登录失败:', error)

        // 处理不同类型的错误
        if (error.response) {
          // API返回的错误
          this.error = error.response.data?.message || '认证失败'
        } else if (error.code) {
          // 带错误代码的错误（来自pywebview API）
          this.error = error.message || '认证失败'
        } else if (error.message) {
          // 普通错误 - 提供更友好的错误信息
          if (error.message.includes('至少需要2个字符')) {
            this.error = '直播间名称至少需要2个字符'
          } else if (error.message.includes('服务器响应错误')) {
            this.error = '无法连接到服务器，请检查网络连接或稍后重试'
          } else if (error.message.includes('认证失败')) {
            this.error = '直播间认证失败，请检查直播间名称是否正确'
          } else {
            this.error = error.message
          }
        } else {
          // 未知错误
          this.error = '登录失败，请检查输入并重试'
        }

        // 清理认证状态
        this.isAuthenticated = false
        this.user = null
        this.sessionId = null
        this.roomInfo = null

        // 清理localStorage
        localStorage.removeItem('session_id')
        localStorage.removeItem('room_info')
        localStorage.removeItem('user_data')
        localStorage.removeItem('auth_token')

        // 返回失败结果，不抛出异常（让组件处理）
        return { success: false, error: this.error, code: error.code, details: error.details }
      } finally {
        this.loading = false
      }
    },

    // 音色选择相关方法
    async selectVoiceModel(voiceId, genderId) {
      try {
        console.log('🎵 选择音色模型:', { voiceId, genderId })

        // 调用pywebview API选择音色
        if (window.pywebview && window.pywebview.api) {
          const result = await window.pywebview.api.selectVoiceModel(voiceId, genderId)
          console.log('📡 音色选择响应:', result)

          if (result && result.success) {
            // 更新选中的音色信息
            const selectedVoice = this.voiceList.find(voice => voice.voiceId === voiceId)
            this.selectedVoice = {
              voiceId,
              genderId,
              voiceName: selectedVoice?.voiceName || '未知音色',
              gameId: selectedVoice?.gameId || ''
            }
            this.voiceSelectionRequired = false

            console.log('✅ 音色选择成功:', this.selectedVoice)
            return { success: true, voice: this.selectedVoice }
          } else {
            console.error('❌ 音色选择失败:', result)
            return { success: false, error: result?.message || '音色选择失败' }
          }
        } else {
          // Mock模式
          console.log('🔧 Mock模式音色选择')
          const selectedVoice = this.voiceList.find(voice => voice.voiceId === voiceId)
          this.selectedVoice = {
            voiceId,
            genderId,
            voiceName: selectedVoice?.voiceName || '测试音色',
            gameId: selectedVoice?.gameId || 'game001'
          }
          this.voiceSelectionRequired = false
          return { success: true, voice: this.selectedVoice }
        }
      } catch (error) {
        console.error('❌ 音色选择异常:', error)
        return { success: false, error: error.message || '音色选择异常' }
      }
    },

    // 重置音色选择状态
    resetVoiceSelection() {
      this.selectedVoice = null
      this.voiceSelectionRequired = true
      console.log('🔄 音色选择状态已重置')
    },

    // 获取指定游戏的音色列表
    getVoicesByGame(gameId) {
      return this.voiceList.filter(voice => voice.gameId === gameId)
    },

    async logout() {
      this.loading = true

      try {
        console.log('🚪 开始完整的退出登录流程:', this.sessionId)

        // 1. 清理所有store状态
        await this.clearAllStoreStates()

        // 2. 调用客户端登出API
        console.log('📡 调用客户端登出API...')
        const response = await authAPI.logout()
        console.log('📡 客户端登出响应:', response.data)

        // 3. 清理认证状态
        this.clearAuth()

        if (response.data && response.data.success) {
          console.log('✅ 登出API调用成功')
          return { success: true, message: response.data.message || '登出成功' }
        } else {
          console.warn('⚠️ 登出API返回失败，但本地状态已清理')
          return { success: false, message: response.data?.message || '登出API返回失败' }
        }
      } catch (error) {
        console.error('❌ 登出API调用失败:', error)
        // 即使API调用失败也要清理本地状态
        await this.clearAllStoreStates()
        this.clearAuth()
        return { success: false, message: error.message || '登出API调用失败' }
      } finally {
        this.loading = false
      }
    },

    // 清理所有store状态
    async clearAllStoreStates() {
      try {
        console.log('🧹 开始清理所有应用状态...')

        // 清理WebSocket store
        const { useWebSocketStore } = await import('@/stores/websocket')
        const websocketStore = useWebSocketStore()

        console.log('🔌 断开WebSocket连接并清理状态...')
        websocketStore.disconnect()
        websocketStore.clearAllData()

        // 清理Process store
        const { useProcessStore } = await import('@/stores/process')
        const processStore = useProcessStore()

        console.log('⏹️ 停止所有进程并清理状态...')
        if (processStore.isMonitoring) {
          await processStore.stopProcessMonitoring()
        }
        if (processStore.isAutomating) {
          await processStore.stopAutomation()
        }
        processStore.clearAllData()

        console.log('✅ 所有应用状态已清理')
      } catch (error) {
        console.error('❌ 清理应用状态时出错:', error)
      }
    },

    clearAuth() {
      this.isAuthenticated = false
      this.user = null
      this.sessionId = null
      this.roomInfo = null
      this.error = null

      // 清理音色相关状态
      this.gameList = []
      this.voiceList = []
      this.selectedVoice = null
      this.voiceSelectionRequired = false

      localStorage.removeItem('session_id')
      localStorage.removeItem('room_info')
      localStorage.removeItem('user_data')

      console.log('✅ 本地状态已清理')
    },

    initializeAuth() {
      const sessionId = localStorage.getItem('session_id')
      const roomInfo = localStorage.getItem('room_info')
      const userData = localStorage.getItem('user_data')

      if (sessionId && roomInfo && userData) {
        this.sessionId = sessionId
        this.roomInfo = JSON.parse(roomInfo)
        this.user = JSON.parse(userData)
        this.isAuthenticated = true

        console.log('🔄 从本地存储恢复认证状态:', { sessionId, roomInfo: this.roomInfo })
      }
    },

    async validateSession() {
      if (!this.sessionId) {
        return false
      }

      try {
        console.log('🔍 验证会话:', this.sessionId)

        const response = await serverAPI.get(`/auth/validate/${this.sessionId}`)

        if (response.data.valid) {
          console.log('✅ 会话验证成功')
          return true
        } else {
          console.log('❌ 会话已失效')
          this.clearAuth()
          return false
        }
      } catch (error) {
        console.error('❌ 会话验证失败:', error)
        this.clearAuth()
        return false
      }
    },

    // 初始化缓存路径配置
    async initializeCachePaths() {
      try {
        console.log('📁 开始初始化缓存路径配置...')

        // 获取项目绝对路径
        let projectPath = ''

        // 检查是否在pywebview环境中
        if (typeof window !== 'undefined' && window.pywebview && window.pywebview.api) {
          try {
            // 使用pywebview API获取项目路径
            const pathResult = await window.pywebview.api.get_project_path()
            if (pathResult && pathResult.success) {
              projectPath = pathResult.path
              console.log('✅ 通过pywebview API获取项目路径:', projectPath)
            } else {
              throw new Error('pywebview API返回失败')
            }
          } catch (apiError) {
            console.warn('⚠️ pywebview API获取路径失败，使用默认路径:', apiError)
            projectPath = '/app'  // 默认路径
          }
        } else {
          // 开发环境下使用模拟路径
          projectPath = process.cwd ? process.cwd() : '/Users/<USER>/Documents/智能主播助手'
          console.log('🔧 开发环境使用模拟项目路径:', projectPath)
        }

        // 定义三个缓存路径
        const cachePaths = {
          screenshot: `${projectPath}/screenshot`,
          voice_cache: `${projectPath}/voice_cache`,
          models: `${projectPath}/models`
        }

        console.log('📁 初始化的缓存路径配置:', cachePaths)

        // 存储到localStorage（临时存储）
        localStorage.setItem('cache_paths', JSON.stringify(cachePaths))

        // TODO: 存储到Redis
        // 这里应该调用API将配置存储到Redis
        // Redis key格式：{房间名称}:cache_paths
        const roomName = this.user?.roomName || 'default'
        console.log(`📝 缓存路径配置应存储到Redis key: ${roomName}:cache_paths`)

        console.log('✅ 缓存路径配置初始化完成')
        return cachePaths

      } catch (error) {
        console.error('❌ 初始化缓存路径配置失败:', error)
        throw error
      }
    },

    // 加载用户配置
    async loadUserConfigs() {
      try {
        console.log('📋 开始加载用户配置...')

        const roomName = this.user?.roomName || 'default'

        // 动态导入configAPI
        const { configAPI } = await import('@/api')

        // 加载快捷键配置
        try {
          console.log('⌨️ 加载快捷键配置...')
          const hotkeysResponse = await configAPI.hotkeys.get(roomName)

          if (hotkeysResponse.data && hotkeysResponse.data.success) {
            const hotkeysConfig = hotkeysResponse.data.data
            console.log('✅ 快捷键配置加载成功:', hotkeysConfig)

            // 存储到localStorage（临时存储）
            localStorage.setItem('hotkeys_config', JSON.stringify(hotkeysConfig))

            // TODO: 将配置传递给Client端进行初始化
            console.log('📝 快捷键配置应传递给Client端进行初始化')
          } else {
            console.warn('⚠️ 快捷键配置加载失败，使用默认配置')
          }
        } catch (hotkeysError) {
          console.error('❌ 加载快捷键配置失败:', hotkeysError)
        }

        // 加载存储路径配置
        try {
          console.log('📁 加载存储路径配置...')
          const pathsResponse = await configAPI.paths.get(roomName)

          if (pathsResponse.data && pathsResponse.data.success) {
            const pathsConfig = pathsResponse.data.data
            console.log('✅ 存储路径配置加载成功:', pathsConfig)

            // 更新localStorage中的路径配置
            localStorage.setItem('cache_paths', JSON.stringify(pathsConfig))

            // TODO: 将配置传递给Client端进行初始化
            console.log('📝 存储路径配置应传递给Client端进行初始化')
          } else {
            console.warn('⚠️ 存储路径配置加载失败，使用默认配置')
          }
        } catch (pathsError) {
          console.error('❌ 加载存储路径配置失败:', pathsError)
        }

        console.log('✅ 用户配置加载完成')

      } catch (error) {
        console.error('❌ 加载用户配置失败:', error)
        throw error
      }
    }
  }
})
