<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon size="48" color="#5865f2">
            <VideoCamera />
          </el-icon>
        </div>
        <h1>智能主播助手</h1>
        <p>直播弹幕监控系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <!-- 用户名输入 -->
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
            :disabled="authStore.loading"
            @keyup.enter="handleLogin"
            @input="handleUsernameChange"
            clearable
          />
          <div v-if="usernameValidation.show" class="input-hint">
            <el-text
              :type="usernameValidation.type"
              size="small"
            >
              {{ usernameValidation.message }}
            </el-text>
          </div>
        </el-form-item>

        <!-- 密码输入 -->
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            :disabled="authStore.loading"
            @keyup.enter="handleLogin"
            @input="handlePasswordChange"
            show-password
            clearable
          />
          <div v-if="passwordValidation.show" class="input-hint">
            <el-text
              :type="passwordValidation.type"
              size="small"
            >
              {{ passwordValidation.message }}
            </el-text>
          </div>
        </el-form-item>

        <!-- 租户选择 -->
        <el-form-item prop="tenantId">
          <el-select
            v-model="loginForm.tenantId"
            placeholder="请选择租户"
            size="large"
            :prefix-icon="OfficeBuilding"
            :disabled="authStore.loading || tenantLoading"
            :loading="tenantLoading"
            @change="handleTenantChange"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="tenant in tenantList"
              :key="tenant.tenantId"
              :label="tenant.tenantName"
              :value="tenant.tenantId"
            />
          </el-select>
          <div v-if="tenantValidation.show" class="input-hint">
            <el-text
              :type="tenantValidation.type"
              size="small"
            >
              {{ tenantValidation.message }}
            </el-text>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            :disabled="!isFormValid"
            class="login-button"
            @click="handleLogin"
          >
            <span v-if="!authStore.loading">连接直播间</span>
            <span v-else>连接中...</span>
          </el-button>
        </el-form-item>
      </el-form>

      <div v-if="authStore.error" class="error-message">
        <el-alert
          :title="authStore.error"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <div class="login-footer">
        <div class="input-tips">
          <h4>登录要求：</h4>
          <ul>
            <li>用户名长度为2-50个字符</li>
            <li>密码长度为6-50个字符</li>
            <li>请选择正确的租户</li>
          </ul>
        </div>
        <div class="features">
          <div class="feature">
            <el-icon><ChatDotRound /></el-icon>
            <span>实时弹幕监控</span>
          </div>
          <div class="feature">
            <el-icon><Setting /></el-icon>
            <span>自动化操作</span>
          </div>
          <div class="feature">
            <el-icon><DataAnalysis /></el-icon>
            <span>意图分析处理</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useWebSocketStore } from '@/stores/websocket'
import { ElMessage } from 'element-plus'
import { User, Lock, OfficeBuilding, VideoCamera, ChatDotRound, Setting, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const websocketStore = useWebSocketStore()

const loginFormRef = ref()
const loginForm = reactive({
  username: '',
  password: '',
  tenantId: ''
})

// 租户数据
const tenantList = ref([])
const tenantLoading = ref(false)

// 实时验证状态
const usernameValidation = reactive({
  show: false,
  type: 'info',
  message: ''
})

const passwordValidation = reactive({
  show: false,
  type: 'info',
  message: ''
})

const tenantValidation = reactive({
  show: false,
  type: 'info',
  message: ''
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度为2-50个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
      message: '用户名只能包含字母、数字、中文、下划线和短横线',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度为6-50个字符', trigger: 'blur' }
  ],
  tenantId: [
    { required: true, message: '请选择租户', trigger: 'change' }
  ]
}

// 表单验证状态
const isFormValid = computed(() => {
  return loginForm.username.trim() &&
         loginForm.password.trim() &&
         loginForm.tenantId.trim()
})

// 加载租户列表
const loadTenantList = async () => {
  tenantLoading.value = true
  try {
    console.log('🏢 加载租户列表...')

    // 调用pywebview API获取租户列表
    if (window.pywebview && window.pywebview.api) {
      const tenants = await window.pywebview.api.getTenantId()
      console.log('📡 租户列表响应:', tenants)

      if (Array.isArray(tenants)) {
        tenantList.value = tenants
        console.log('✅ 租户列表加载成功:', tenants.length, '个租户')
      } else {
        console.warn('⚠️ 租户列表格式不正确:', tenants)
        ElMessage.warning('租户列表格式不正确')
      }
    } else {
      // Mock数据用于开发测试
      console.log('🔧 使用Mock租户数据')
      tenantList.value = [
        { tenantId: 'tenant001', tenantName: '测试租户1' },
        { tenantId: 'tenant002', tenantName: '测试租户2' },
        { tenantId: 'tenant003', tenantName: '演示租户' }
      ]
    }
  } catch (error) {
    console.error('❌ 加载租户列表失败:', error)
    ElMessage.error('加载租户列表失败，请刷新页面重试')
  } finally {
    tenantLoading.value = false
  }
}

// 输入验证方法
const handleUsernameChange = () => {
  const username = loginForm.username.trim()

  if (!username) {
    usernameValidation.show = false
    return
  }

  if (username.length < 2) {
    usernameValidation.show = true
    usernameValidation.type = 'warning'
    usernameValidation.message = '用户名至少需要2个字符'
  } else if (username.length > 50) {
    usernameValidation.show = true
    usernameValidation.type = 'danger'
    usernameValidation.message = '用户名不能超过50个字符'
  } else if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(username)) {
    usernameValidation.show = true
    usernameValidation.type = 'danger'
    usernameValidation.message = '用户名只能包含字母、数字、中文、下划线和短横线'
  } else {
    usernameValidation.show = true
    usernameValidation.type = 'success'
    usernameValidation.message = '用户名格式正确'
  }
}

const handlePasswordChange = () => {
  const password = loginForm.password.trim()

  if (!password) {
    passwordValidation.show = false
    return
  }

  if (password.length < 6) {
    passwordValidation.show = true
    passwordValidation.type = 'warning'
    passwordValidation.message = '密码至少需要6个字符'
  } else if (password.length > 50) {
    passwordValidation.show = true
    passwordValidation.type = 'danger'
    passwordValidation.message = '密码不能超过50个字符'
  } else {
    passwordValidation.show = true
    passwordValidation.type = 'success'
    passwordValidation.message = '密码长度符合要求'
  }
}

const handleTenantChange = () => {
  if (loginForm.tenantId) {
    const selectedTenant = tenantList.value.find(t => t.tenantId === loginForm.tenantId)
    if (selectedTenant) {
      tenantValidation.show = true
      tenantValidation.type = 'success'
      tenantValidation.message = `已选择：${selectedTenant.tenantName}`
    }
  } else {
    tenantValidation.show = false
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 前端验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    // 额外的前端验证
    const { username, password, tenantId } = loginForm
    if (!username.trim() || !password.trim() || !tenantId.trim()) {
      ElMessage.error('请填写完整的登录信息')
      return
    }

    console.log('🔐 尝试登录:', { username, tenantId })
    const result = await authStore.login({
      username: username.trim(),
      password: password.trim(),
      tenantId: tenantId.trim()
    })

    if (result.success) {
      ElMessage.success('登录成功！')

      // 建立WebSocket连接
      websocketStore.connect(result.session_id)

      // 确保状态更新后再导航
      await nextTick()

      // 导航到主页面
      await router.push('/dashboard')
    } else {
      // 显示具体的错误信息
      const errorMessage = result.error || authStore.error || '登录失败'
      ElMessage.error(errorMessage)

      // 记录详细错误信息
      console.error('❌ 登录失败:', {
        error: result.error,
        code: result.code,
        details: result.details,
        storeError: authStore.error
      })
    }
  } catch (error) {
    console.error('Login error:', error)

    // 根据错误类型显示不同的错误信息
    let errorMessage = '登录失败，请重试'

    if (error.message) {
      if (error.message.includes('网络') || error.message.includes('连接')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('服务器')) {
        errorMessage = '服务器连接失败，请稍后重试'
      } else if (error.message.includes('认证') || error.message.includes('验证')) {
        errorMessage = '认证失败，请检查直播间名称'
      } else {
        errorMessage = error.message
      }
    }

    ElMessage.error(errorMessage)
  }
}

// 组件挂载时加载租户列表
onMounted(async () => {
  console.log('✓ LoginView mounted')
  await loadTenantList()
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
  padding: 20px;
}

.login-card {
  background: rgba(45, 45, 68, 0.95);
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 480px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
}

.login-header h1 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-header p {
  color: #a0a0a0;
  font-size: 16px;
}

.login-form {
  margin-bottom: 20px;
}

.input-hint {
  margin-top: 4px;
  padding-left: 4px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
  border: none;
  border-radius: 8px;
}

.login-button:hover {
  background: linear-gradient(135deg, #4752c4 0%, #3c47a3 100%);
}

.error-message {
  margin-bottom: 20px;
}

.login-footer {
  text-align: center;
  color: #a0a0a0;
}

.input-tips {
  margin-bottom: 20px;
  text-align: left;
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #5865f2;
}

.input-tips h4 {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 600;
}

.input-tips ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.input-tips li {
  color: #b0b0b0;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.features {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #c0c0c0;
  flex: 1;
  min-width: 120px;
  text-align: center;
}

.feature .el-icon {
  color: #5865f2;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .features {
    flex-direction: column;
    gap: 12px;
  }

  .feature {
    flex: none;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .features {
    gap: 10px;
  }

  .feature {
    font-size: 13px;
  }

  .feature .el-icon {
    font-size: 14px;
  }
}

/* Element Plus customization */
:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: none;
}

:deep(.el-input__wrapper:hover) {
  border-color: #5865f2;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #5865f2;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
}

:deep(.el-input__inner) {
  color: #ffffff;
  font-size: 16px;
}

:deep(.el-input__inner::placeholder) {
  color: #a0a0a0;
}

:deep(.el-form-item__error) {
  color: #f56565;
}
</style>
