<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon size="48" color="#5865f2">
            <VideoCamera />
          </el-icon>
        </div>
        <h1>智能主播助手</h1>
        <p>直播弹幕监控系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="roomName">
          <el-input
            v-model="loginForm.roomName"
            placeholder="请输入直播间名称（至少2个字符）"
            size="large"
            :prefix-icon="User"
            :disabled="authStore.loading"
            @keyup.enter="handleLogin"
            @input="handleInputChange"
            clearable
          />
          <div v-if="inputValidation.show" class="input-hint">
            <el-text
              :type="inputValidation.type"
              size="small"
            >
              {{ inputValidation.message }}
            </el-text>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="authStore.loading"
            :disabled="!loginForm.roomName.trim()"
            class="login-button"
            @click="handleLogin"
          >
            <span v-if="!authStore.loading">连接直播间</span>
            <span v-else>连接中...</span>
          </el-button>
        </el-form-item>
      </el-form>

      <div v-if="authStore.error" class="error-message">
        <el-alert
          :title="authStore.error"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <div class="login-footer">
        <div class="input-tips">
          <h4>输入要求：</h4>
          <ul>
            <li>直播间名称至少需要2个字符</li>
            <li>支持字母、数字、中文、下划线和短横线</li>
            <li>建议使用真实的直播间名称</li>
          </ul>
        </div>
        <div class="features">
          <div class="feature">
            <el-icon><ChatDotRound /></el-icon>
            <span>实时弹幕监控</span>
          </div>
          <div class="feature">
            <el-icon><Setting /></el-icon>
            <span>自动化操作</span>
          </div>
          <div class="feature">
            <el-icon><DataAnalysis /></el-icon>
            <span>意图分析处理</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useWebSocketStore } from '@/stores/websocket'
import { ElMessage } from 'element-plus'
import { User, VideoCamera, ChatDotRound, Setting, DataAnalysis } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const websocketStore = useWebSocketStore()

const loginFormRef = ref()
const loginForm = reactive({
  roomName: ''
})

// 实时验证状态
const inputValidation = reactive({
  show: false,
  type: 'info',
  message: ''
})

const loginRules = {
  roomName: [
    { required: true, message: '请输入直播间名称', trigger: 'blur' },
    { min: 2, max: 50, message: '直播间名称至少需要2个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
      message: '直播间名称只能包含字母、数字、中文、下划线和短横线',
      trigger: 'blur'
    }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 前端验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    // 额外的前端验证
    const roomName = loginForm.roomName.trim()
    if (!roomName) {
      ElMessage.error('请输入直播间名称')
      return
    }

    if (roomName.length < 2) {
      ElMessage.error('直播间名称至少需要2个字符')
      return
    }

    console.log('Attempting login with room:', roomName)
    const result = await authStore.login(roomName)

    if (result.success) {
      ElMessage.success('成功连接到直播间！')

      // Establish WebSocket connection with session_id
      websocketStore.connect(result.session_id)

      // Use nextTick to ensure state is updated before navigation
      await nextTick()

      // Navigate to dashboard
      await router.push('/dashboard')
    } else {
      // 显示具体的错误信息
      const errorMessage = result.error || authStore.error || '登录失败'
      ElMessage.error(errorMessage)

      // 记录详细错误信息
      console.error('Login failed:', {
        error: result.error,
        code: result.code,
        details: result.details,
        storeError: authStore.error
      })
    }
  } catch (error) {
    console.error('Login error:', error)

    // 根据错误类型显示不同的错误信息
    let errorMessage = '登录失败，请重试'

    if (error.message) {
      if (error.message.includes('网络') || error.message.includes('连接')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message.includes('服务器')) {
        errorMessage = '服务器连接失败，请稍后重试'
      } else if (error.message.includes('认证') || error.message.includes('验证')) {
        errorMessage = '认证失败，请检查直播间名称'
      } else {
        errorMessage = error.message
      }
    }

    ElMessage.error(errorMessage)
  }
}

// 处理输入变化的实时验证
const handleInputChange = (value) => {
  const trimmedValue = value.trim()

  if (!trimmedValue) {
    inputValidation.show = false
    return
  }

  if (trimmedValue.length < 2) {
    inputValidation.show = true
    inputValidation.type = 'warning'
    inputValidation.message = '直播间名称至少需要2个字符'
  } else if (trimmedValue.length > 50) {
    inputValidation.show = true
    inputValidation.type = 'warning'
    inputValidation.message = '直播间名称不能超过50个字符'
  } else if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(trimmedValue)) {
    inputValidation.show = true
    inputValidation.type = 'warning'
    inputValidation.message = '只能包含字母、数字、中文、下划线和短横线'
  } else {
    inputValidation.show = true
    inputValidation.type = 'success'
    inputValidation.message = '直播间名称格式正确'
  }
}

onMounted(() => {
  console.log('✓ LoginView mounted')
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
  padding: 20px;
}

.login-card {
  background: rgba(45, 45, 68, 0.95);
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 480px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
}

.login-header h1 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-header p {
  color: #a0a0a0;
  font-size: 16px;
}

.login-form {
  margin-bottom: 20px;
}

.input-hint {
  margin-top: 4px;
  padding-left: 4px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #5865f2 0%, #4752c4 100%);
  border: none;
  border-radius: 8px;
}

.login-button:hover {
  background: linear-gradient(135deg, #4752c4 0%, #3c47a3 100%);
}

.error-message {
  margin-bottom: 20px;
}

.login-footer {
  text-align: center;
  color: #a0a0a0;
}

.input-tips {
  margin-bottom: 20px;
  text-align: left;
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #5865f2;
}

.input-tips h4 {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 600;
}

.input-tips ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.input-tips li {
  color: #b0b0b0;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.features {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #c0c0c0;
  flex: 1;
  min-width: 120px;
  text-align: center;
}

.feature .el-icon {
  color: #5865f2;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .features {
    flex-direction: column;
    gap: 12px;
  }

  .feature {
    flex: none;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .features {
    gap: 10px;
  }

  .feature {
    font-size: 13px;
  }

  .feature .el-icon {
    font-size: 14px;
  }
}

/* Element Plus customization */
:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: none;
}

:deep(.el-input__wrapper:hover) {
  border-color: #5865f2;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #5865f2;
  box-shadow: 0 0 0 2px rgba(88, 101, 242, 0.2);
}

:deep(.el-input__inner) {
  color: #ffffff;
  font-size: 16px;
}

:deep(.el-input__inner::placeholder) {
  color: #a0a0a0;
}

:deep(.el-form-item__error) {
  color: #f56565;
}
</style>
